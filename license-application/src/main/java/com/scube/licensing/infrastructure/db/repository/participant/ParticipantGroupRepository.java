package com.scube.licensing.infrastructure.db.repository.participant;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.participant.type.ParticipantGroup;
import jakarta.validation.constraints.Size;

public interface ParticipantGroupRepository extends AuditableEntityRepository<ParticipantGroup, Long> {
    ParticipantGroup findByNameIgnoreCase(@Size(max = 255) String name);
}
