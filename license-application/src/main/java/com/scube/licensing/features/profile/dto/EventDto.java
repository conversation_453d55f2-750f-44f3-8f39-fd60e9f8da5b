package com.scube.licensing.features.profile.dto;

import com.scube.licensing.features.events.Event;
import com.scube.licensing.infrastructure.db.entity.event.EventType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Map;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EventDto {
    private UUID uuid;
    private Long eventTypeId;
    private String code;
    private String name;
    private String description;
    private String createdBy;
    private Instant createdDate;
    private String comment;
    private String action;
    private Map<String, String> metadata;

    public EventDto(EventType eventType, Event event) {
        if (event != null) {
            this.uuid = event.getUuid();
            this.createdBy = event.getCreatedBy();
            this.createdDate = event.getCreatedDate();
            this.comment = event.getComment();
            this.action = event.getAction();
        }

        if (eventType != null) {
            this.code = eventType.getCode();
            this.name = eventType.getName();
            this.description = eventType.getDescription();
        }
    }
}
