package com.scube.licensing.features.license.delete;

import com.scube.licensing.features.license.LicenseService;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.UUID;


@Component
@AllArgsConstructor
@Slf4j
public class DeleteLicenseCommandHandler extends FanoutListener<DeleteLicenseCommandHandler.DeleteLicenseCommand> {
    private final LicenseService licenseService;
    private final AmqpGateway amqpGateway;

    public void consume(DeleteLicenseCommand command) {
        log.debug("DeleteLicenseCommandHandler.consume: {}", command.entityId());
        try {
            licenseService.deleteLicense(command.entityId());
        } catch (Exception e) {
            log.error("DeleteLicenseCommandHandler.consume: {}", e.getMessage(), e);
        }
        amqpGateway.publish(new DeleteItemCommandEvent(command.entityId()));
    }

    public record DeleteLicenseCommand(UUID entityId) implements IRabbitFanoutSubscriber {
    }

    public record DeleteItemCommandEvent(UUID itemEntityId) implements IRabbitFanoutPublisher {
    }
}