package com.scube.licensing.features.license.reports;

import com.scube.licensing.infrastructure.db.repository.license.LicenseActivityRepository;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

@AllArgsConstructor
@Component
@Slf4j
public class GetLicenseActivitiesByOrderIdQueryListener extends FanoutListenerRpc<GetLicenseActivitiesByOrderIdQueryListener.GetLicenseActivitiesByOrderIdQuery, GetLicenseActivitiesByOrderIdQueryListener.GetLicenseActivitiesByOrderIdQueryResponse> {
    private final LicenseActivityRepository licenseActivityRepository;

    public RabbitResult<GetLicenseActivitiesByOrderIdQueryResponse> consume(GetLicenseActivitiesByOrderIdQueryListener.GetLicenseActivitiesByOrderIdQuery event) {
        return RabbitResult.of(() -> new GetLicenseActivitiesByOrderIdQueryResponse(licenseActivityRepository.getActivitiesByOrderId(UUID.fromString(event.orderId))));
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class GetLicenseActivitiesByOrderIdQuery implements IRabbitFanoutSubscriberRpc<GetLicenseActivitiesByOrderIdQueryResponse> {
        private String orderId;
    }

    public record GetLicenseActivitiesByOrderIdQueryResponse(List<String> licenseActivities) {
    }
}







