<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="updateDefaultAuditRecord3" author="david">
        <sqlFile path="update_table_with_createdBy_and_modified_by.sql"
                 relativeToChangelogFile="true" splitStatements="false"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="removeViews">
        <sql splitStatements="true">
            DROP VIEW IF EXISTS view_pivoted_license;
            DROP VIEW IF EXISTS view_dog_licenses;
            DROP VIEW IF EXISTS view_participant;
            DROP VIEW IF EXISTS view_contact;
            DROP VIEW IF EXISTS view_custom_field_value;
            drop function if exists get_participants;
            drop function if exists get_dogs;
            drop function if exists get_parcels;
        </sql>
    </changeSet>

    <changeSet author="davidr (generated)" id="1694653229094-241">
        <renameColumn tableName="app_properties" oldColumnName="uuid" newColumnName="app_properties_id"
                      columnDataType="uuid"/>
    </changeSet>
    <changeSet id="1694653229094-2410" author="davidr">
        <modifyDataType columnName="app_properties_id" newDataType="uuid" tableName="app_properties"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-242">
        <renameColumn tableName="association_value_entry" oldColumnName="id" newColumnName="association_value_entry_id"
                      columnDataType="int8"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-1">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="address"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-2">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="app_properties"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-3">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="association"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-4">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="contact"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-5">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="contact_group"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-6">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="contact_type"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-7">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="contact_type_group"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-8">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="custom_entity_instance"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-9">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="custom_entity_sub_type"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-10">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="custom_entity_type"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-11">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="custom_field_value"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-12">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="license"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-13">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="license_fee"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-14">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="license_status"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-15">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="license_type"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-16">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="license_type_fee"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-17">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by"
                              tableName="license_type_fee_conditional" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-18">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="license_type_setting"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-19">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="multi_form_argument"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-20">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="multi_form_body_field"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-21">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="multi_form_builder"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-22">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by"
                              tableName="multi_form_conditional_display" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-23">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by"
                              tableName="multi_form_element_options" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-24">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="multi_form_form_element"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-25">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by"
                              tableName="multi_form_on_form_submit_api" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-26">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by"
                              tableName="multi_form_on_page_next_api" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-27">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="multi_form_page"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-28">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="multi_form_query_string"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-29">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="multi_form_request_body"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-30">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="multi_form_request_slug"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-31">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="multi_form_response"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-32">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by"
                              tableName="multi_form_response_error" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-33">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by"
                              tableName="multi_form_response_field" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-34">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by"
                              tableName="multi_form_response_success" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-35">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="multi_form_section"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-36">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="multi_form_validation"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-37">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="participant"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-38">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="participant_address"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-39">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="participant_address_type"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-40">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="participant_group"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-41">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="participant_type"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-42">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="participant_type_group"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-43">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="profile_form_argument"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-44">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by"
                              tableName="profile_form_argument_filter" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-45">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="profile_form_builder"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-46">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="profile_form_element"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-47">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="profile_form_section"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-48">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by"
                              tableName="profile_form_section_group" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-49">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="profile_type"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-50">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="search_form_argument"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-51">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by"
                              tableName="search_form_argument_filter" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-52">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="search_form_builder"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-53">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="search_form_element"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-54">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="search_form_section"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-55">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="table_column_field"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-56">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="table_custom_field"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-57">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="table_type"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-58">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="created_by" tableName="tenant" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-59">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="address"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-60">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="app_properties"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-61">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="association"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-62">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="contact"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-63">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="contact_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-64">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="contact_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-65">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="contact_type_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-66">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="custom_entity_instance"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-67">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="custom_entity_sub_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-68">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="custom_entity_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-69">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="custom_field_value"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-70">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-71">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-72">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_status"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-73">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-74">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_type_fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-75">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_type_fee_conditional"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-76">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="license_type_setting"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-77">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-78">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_body_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-79">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-80">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_conditional_display"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-81">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_element_options"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-82">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-83">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_on_form_submit_api"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-84">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_on_page_next_api"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-85">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_page"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-86">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_query_string"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-87">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_request_body"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-88">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_request_slug"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-89">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_response"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-90">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_response_error"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-91">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_response_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-92">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_response_success"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-93">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-94">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="multi_form_validation"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-95">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-96">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_address"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-97">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_address_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-98">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-99">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-100">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="participant_type_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-101">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-102">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_form_argument_filter"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-103">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-104">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-105">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-106">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_form_section_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-107">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="profile_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-108">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-109">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_argument_filter"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-110">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-111">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-112">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="search_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-113">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="table_column_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-114">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="table_custom_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-115">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="table_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-116">
        <modifyDataType columnName="created_date" newDataType="timestamp" tableName="tenant"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-117">
        <modifyDataType columnName="enqueued_at" newDataType="timestamp" tableName="dead_letter_entry"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-118">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="address"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-119">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="app_properties"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-120">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="association"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-121">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="contact"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-122">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="contact_group"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-123">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="contact_type"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-124">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="contact_type_group"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-125">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="custom_entity_instance" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-126">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="custom_entity_sub_type" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-127">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="custom_entity_type"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-128">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="custom_field_value"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-129">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="license"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-130">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="license_fee"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-131">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="license_status"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-132">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="license_type"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-133">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="license_type_fee"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-134">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="license_type_fee_conditional" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-135">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="license_type_setting" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-136">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="multi_form_argument" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-137">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="multi_form_body_field" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-138">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="multi_form_builder"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-139">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="multi_form_conditional_display" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-140">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="multi_form_element_options" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-141">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="multi_form_form_element" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-142">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="multi_form_on_form_submit_api" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-143">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="multi_form_on_page_next_api" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-144">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="multi_form_page"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-145">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="multi_form_query_string" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-146">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="multi_form_request_body" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-147">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="multi_form_request_slug" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-148">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="multi_form_response" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-149">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="multi_form_response_error" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-150">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="multi_form_response_field" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-151">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="multi_form_response_success" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-152">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="multi_form_section"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-153">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="multi_form_validation" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-154">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="participant"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-155">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="participant_address" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-156">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="participant_address_type" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-157">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="participant_group"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-158">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="participant_type"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-159">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="participant_type_group" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-160">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="profile_form_argument" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-161">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="profile_form_argument_filter" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-162">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="profile_form_builder" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-163">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="profile_form_element" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-164">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="profile_form_section" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-165">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="profile_form_section_group" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-166">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="profile_type"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-167">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="search_form_argument" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-168">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="search_form_argument_filter" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-169">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="search_form_builder" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-170">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="search_form_element" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-171">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by"
                              tableName="search_form_section" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-172">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="table_column_field"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-173">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="table_custom_field"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-174">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="table_type"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-175">
        <addNotNullConstraint columnDataType="varchar(250)" columnName="last_modified_by" tableName="tenant"
                              validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-176">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="address"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-177">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="app_properties"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-178">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="association"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-179">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="contact"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-180">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="contact_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-181">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="contact_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-182">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="contact_type_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-183">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="custom_entity_instance"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-184">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="custom_entity_sub_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-185">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="custom_entity_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-186">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="custom_field_value"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-187">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-188">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-189">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_status"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-190">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-191">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_type_fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-192">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="license_type_fee_conditional"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-193">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="license_type_setting"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-194">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-195">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_body_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-196">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-197">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="multi_form_conditional_display"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-198">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_element_options"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-199">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-200">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="multi_form_on_form_submit_api"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-201">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="multi_form_on_page_next_api"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-202">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_page"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-203">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_query_string"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-204">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_request_body"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-205">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_request_slug"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-206">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_response"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-207">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_response_error"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-208">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_response_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-209">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="multi_form_response_success"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-210">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-211">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="multi_form_validation"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-212">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-213">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_address"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-214">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_address_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-215">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-216">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-217">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="participant_type_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-218">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="profile_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-219">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="profile_form_argument_filter"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-220">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="profile_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-221">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="profile_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-222">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="profile_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-223">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="profile_form_section_group"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-224">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="profile_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-225">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="search_form_argument"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-226">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp"
                        tableName="search_form_argument_filter"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-227">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="search_form_builder"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-228">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="search_form_element"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-229">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="search_form_section"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-230">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="table_column_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-231">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="table_custom_field"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-232">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="table_type"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-233">
        <modifyDataType columnName="last_modified_date" newDataType="timestamp" tableName="tenant"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-234">
        <modifyDataType columnName="last_touched" newDataType="timestamp" tableName="dead_letter_entry"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-235">
        <modifyDataType columnName="processing_started" newDataType="timestamp" tableName="dead_letter_entry"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-236">
        <dropPrimaryKey tableName="app_properties"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-237">
        <addPrimaryKey columnNames="app_properties_id" constraintName="app_properties_pkey" tableName="app_properties"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-238">
        <dropPrimaryKey tableName="association_value_entry"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694653229094-239">
        <addPrimaryKey columnNames="association_value_entry_id" constraintName="association_value_entry_pkey"
                       tableName="association_value_entry"/>
    </changeSet>
</databaseChangeLog>