<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="addColumn_webhook_inbox_payment_provider" author="David">
        <addColumn tableName="webhook_inbox">
            <column name="payment_provider" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet id="addColumn_audit_log_webhook_inbox_payment_provider" author="David">
        <addColumn tableName="audit_log_webhook_inbox">
            <column name="payment_provider" type="varchar(255)"/>
        </addColumn>
    </changeSet>
    <changeSet id="updateColumn_webhook_inbox_payment_provider" author="David">
        <sql>
            UPDATE payment.webhook_inbox
            SET payment_provider = p.name
            FROM payment.payment_provider p
            WHERE p.payment_provider_id = payment.webhook_inbox.payment_provider_id;
        </sql>
    </changeSet>
    <changeSet id="updateColumn_audit_log_webhook_inbox_payment_provider" author="David">
        <sql>
            UPDATE payment.audit_log_webhook_inbox
            SET payment_provider = p.name
            FROM payment.payment_provider p
            WHERE p.payment_provider_id = payment.audit_log_webhook_inbox.payment_provider_id;
        </sql>
    </changeSet>
    <!-- remove all constraint from webhook.payment_provider_id then delete the column   -->
    <changeSet id="removeColumn_webhook_inbox_payment_provider_id" author="David">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="webhook_inbox" columnName="payment_provider_id"/>
        </preConditions>
        <dropColumn tableName="webhook_inbox" columnName="payment_provider_id"/>
    </changeSet>

    <changeSet id="removeColumn_audit_log_webhook_inbox_payment_provider_id" author="David">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="audit_log_webhook_inbox" columnName="payment_provider_id"/>
        </preConditions>
        <dropColumn tableName="audit_log_webhook_inbox" columnName="payment_provider_id"/>
    </changeSet>

    <!--    drop column webhook.payment_provider_id -->
    <changeSet id="dropColumn_webhook_inbox_payment_provider_id" author="David">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="webhook_inbox" columnName="payment_provider_id"/>
        </preConditions>
        <dropColumn tableName="webhook_inbox" columnName="payment_provider_id"/>
    </changeSet>
    <changeSet id="dropColumn_audit_log_webhook_inbox_payment_provider_id" author="David">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="audit_log_webhook_inbox" columnName="payment_provider_id"/>
        </preConditions>
        <dropColumn tableName="audit_log_webhook_inbox" columnName="payment_provider_id"/>
    </changeSet>
    <!--    drop column payment.payment_provider_id -->
    <changeSet id="dropColumn_payment_provider_id" author="David">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="payment" columnName="payment_provider_id"/>
        </preConditions>
        <dropColumn tableName="payment" columnName="payment_provider_id"/>
    </changeSet>
    <changeSet id="dropColumn_audit_log_payment_payment_provider_id" author="David">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="audit_log_payment" columnName="payment_provider_id"/>
        </preConditions>
        <dropColumn tableName="audit_log_payment" columnName="payment_provider_id"/>
    </changeSet>
    <!--   drop tale payment_provider -->
    <changeSet id="dropTable_payment_provider" author="David">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="payment_provider"/>
        </preConditions>
        <dropTable tableName="payment_provider"/>
    </changeSet>
    <changeSet id="dropTable_audit_log_payment_provider" author="David">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="audit_log_payment_provider"/>
        </preConditions>
        <dropTable tableName="audit_log_payment_provider"/>
    </changeSet>

</databaseChangeLog>