import React, { useEffect, useRef, useState } from "react";
import { FiColumns, FiFilter } from "react-icons/fi";
import Dropdown from "../ui/dropdown/Dropdown";

interface Column {
  id: string;
  columnDef: any;
  getIsVisible: () => boolean;
  getToggleVisibilityHandler: () => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => void;
}

interface Table {
  getIsAllColumnsVisible: () => boolean;
  getToggleAllColumnsVisibilityHandler: () => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => void;
  getAllLeafColumns: () => Column[];
}

interface ColumnFilterDropdownProps {
  table: Table;
}

const ColumnFilterDropdown: React.FC<ColumnFilterDropdownProps> = ({ table }) => {
  return (
    <Dropdown
      trigger={
        <button 
          className="px-2 py-1 h-full shrink-0 bg-white hover:bg-blue-100 border border-black rounded flex gap-1 items-center text-sm font-semibold text-neutral-800"
        >
          <FiColumns /> 
          Columns
        </button>
      }
    >
      <div className="absolute top-11 right-0 z-10 py-2 w-[240px] rounded-md shadow shadow-neutral-500 bg-white">
          {/* <div className="py-2 border border-b-2 shrink-0">
            <label className="block px-4">
              <input
                {...{
                  type: 'checkbox',
                  checked: table.getIsAllColumnsVisible(),
                  onChange: table.getToggleAllColumnsVisibilityHandler(),
                }}
              />{' '}
              Toggle All
            </label>
            <div className='px-4 font-semibold text-neutral-800 shrink-0'>
              Filters:
            </div>
          </div> */}
        {table.getAllLeafColumns().map((column) => (
          <div key={column.id} className="shrink-0">
            <label className="block px-4 shrink-0 py-0.5 font-semibold">
              <input
                type="checkbox"
                checked={column.getIsVisible()}
                onChange={column.getToggleVisibilityHandler()}
                className="mr-1"
              />{" "}
              {column.columnDef.header}
            </label>
          </div>
        ))}
      </div>
    </Dropdown>
  );
};

export default ColumnFilterDropdown;
