"use client"

import { useEffect } from 'react'
import { useAtom } from 'jotai'
import { toast<PERSON>tom } from '@/components/ui/toast/toast'
import type { ToastType } from '@/components/ui/toast/toast'
import { AiFillCheckCircle, AiOutlineInfoCircle, AiFillWarning, AiOutlineClose } from 'react-icons/ai'
import { MdOutlineError } from 'react-icons/md'
import { AnimatePresence, motion } from 'framer-motion'

export const ToastPositions = {
  'top-right': '',
  'top-left': '',
  'bottom-right': '',
  'bottom-left': '',
  'top': '',
  'bottom': '',
} as const;

export const ToastStatuses = {
  'success': '',
  'error': '',
  'warning': '',
  'info': '',
} as const;

const validStatuses = Object.keys(ToastStatuses);
const validPositions = Object.keys(ToastPositions);

const ToastContainer = () => {
  const [toast, setToast] = use<PERSON>tom(toast<PERSON>tom)

  useEffect(() => {
    let toastTimer:NodeJS.Timeout | undefined;
    const duration = toast?.duration ?? 5000;
    if (toast) {
      toastTimer = setTimeout(() => {
        setToast(null);
      }, duration);
    }
    
    return () => clearTimeout(toastTimer);
  }, [toast, setToast]);
  

  const position = {
    'top': 'top-14 transform -translate-x-1/2 left-1/2',
    'top-right': 'top-14 right-6',
    'top-left': 'top-14 left-6',
    'bottom': 'bottom-14 transform -translate-x-1/2 left-1/2',
    'bottom-right': 'bottom-14 right-6',
    'bottom-left': 'bottom-14 left-6',
  }

  const status = {
    info: 'bg-cyan-500 text-cyan-200',
    success: 'bg-green-500 text-green-200 ',
    warning: 'bg-amber-500 text-amber-200',
    error: 'bg-red-500 text-red-200',
  }

  const typeBackground = {
    info: 'bg-cyan-50 border-cyan-300',
    success: 'bg-green-50 ',
    warning: 'bg-amber-50 border-amber-300',
    error: 'bg-red-50',
  }

  const typeTextColor = {
    info: 'text-cyan-700',
    success: 'text-green-700 ',
    warning: 'text-amber-700',
    error: 'text-red-700',
  }

  const icon = {
    info: <AiOutlineInfoCircle />,
    success: <AiFillCheckCircle />,
    warning: <AiFillWarning />,
    error: <MdOutlineError />,
  }

  const toastStatus = toast && validStatuses.includes(toast?.status ?? 'info') ? toast.status : 'info';
  const toastPosition = toast && validPositions.includes(toast?.position ?? 'top-right') ? toast.position : 'top-right';  

  return (
    <AnimatePresence>
      {toast && (
        <div className={`fixed ${position[toastPosition ?? 'top-right']} z-[99999]`}>
          <motion.div
            initial={{ opacity: 0, scale: 0.7, rotate: 0 }}
            animate={{ opacity: 1, scale: [1, 1.5, 1, 1], rotate: [0, -2, 2, 0]}}
            exit={{ opacity: 0, scale: 0.7 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex items-center justify-center w-full h-full">
              <div className={`${typeBackground[toastStatus ?? 'info']} rounded-lg shadow-lg p-2 border-2`}>
                <div className="flex flex-row items-center">

                  {/* Icons */}
                  <div className={`
                    flex items-center justify-center h-8 w-8  rounded-full flex-shrink-0
                    ${status[toastStatus ?? 'info']}
                  `}>
                    {icon[toastStatus ?? 'info']}
                  </div>

                  {/* Messages */}
                  <div className="ml-4 mr-8">
                    <div className="font-bold">{toast.label ?? ''}</div>
                    <div className={`${typeTextColor[toastStatus ?? 'info']}`}>{toast.message ?? 'No Message'}</div>
                  </div>

                  {/* Close */}
                  <div className="flex items-center justify-center h-8 w-8 rounded-full flex-shrink-0">
                    <motion.button
                      className="w-full h-full rounded-full focus:outline-none"
                      onClick={() => setToast(null)}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <AiOutlineClose /> 
                    </motion.button>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  )
}

export default ToastContainer