import {
  ProfileGrid,
  ProfileGridItem,
  ProfileSection,
  createFormData,
  ProfileCustomSelectInput,
  ProfileSelectInput,
  ProfileInput,
  ProfileDateInput,
  ProfileDocument,
} from "@/components/profile/helpers/Setup";
import { useUpdateEntityDogProfile } from "@/hooks/api/useProfiles";
import { Dog } from "@/types/DogType";
import { useGetSettingsByOption } from "@/hooks/api/useAdmin";
import { EditProvider, useEditContext } from "../context/EditContext";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

interface DogFormData {
  veterinaryName: string;
  vaccineDatesExempt: string;
  rabiesTagNumber: string;
  vaccineName: string;
  vaccineProducer: string;
  vaccineBrand: string;
  vaccineAdministeredDate: string;
  vaccineDueDate: string;
  vaccineLotNumber: string;
  vaccineLotExpirationDate: string;
  dogRabiesVaccinationDocument: any;
  dogRabiesVaccinationExemptionDocument: any;
}

const RabiesDogInformation = ({
  dog,
  entityRefetch,
  entityIsFetching,
}: {
  dog: Dog;
  entityRefetch: () => void;
  entityIsFetching: boolean;
}) => {
  const updateEntity = useUpdateEntityDogProfile();

  const defaultValues: DogFormData = {
    veterinaryName: dog.veterinaryName || "",
    vaccineDatesExempt: dog.vaccineDatesExempt ? "yes" : "no",
    rabiesTagNumber: dog.rabiesTagNumber || "",
    vaccineName: dog.vaccineName || "",
    vaccineProducer: dog.vaccineProducer || "",
    vaccineBrand: dog.vaccineBrand || "",
    vaccineAdministeredDate: dog.vaccineAdministeredDate || "",
    vaccineDueDate: dog.vaccineDueDate || "",
    vaccineLotNumber: dog.vaccineLotNumber || "",
    vaccineLotExpirationDate: dog.vaccineLotExpirationDate || "",
    dogRabiesVaccinationDocument:
      dog?.documents?.find(
        (doc) => doc.key === "dogRabiesVaccinationDocument",
      ) || null,
    dogRabiesVaccinationExemptionDocument:
      dog?.documents?.find((doc) => {
        doc.key === "dogRabiesVaccinationExemptionDocument";
      }) || null,
  };

  return (
    <EditProvider
      entity={dog}
      entityId={dog.entityId}
      entityType={dog.entityType}
      entityRefetch={entityRefetch}
      entityIsFetching={entityIsFetching}
      defaultValues={defaultValues}
      updateEntity={updateEntity}
    >
      <Content updateEntity={updateEntity} />
    </EditProvider>
  );
};

export default RabiesDogInformation;

const Content = ({ updateEntity }: { updateEntity: any }) => {
  const { entity } = useEditContext();
  const currentEntity:Dog = entity
  const { watchField } = useEditContext();
  const rejectedFields = entity?.rejectedFields || [];

  // Select Options
  const {
    data: rabiesBrands,
    error: rabiesBrandsError,
    isLoading: rabiesBrandsIsLoading,
  } = useGetSettingsByOption("entity", "dog", "rabies_brand");

  const {
    data: rabiesProducers,
    error: rabiesProducersError,
    isLoading: rabiesProducersIsLoading,
  } = useGetSettingsByOption("entity", "dog", "rabies_producer");

  const onSubmit = (data: any) => {
    let newData = {
      ...data,
      vaccineDatesExempt: data.vaccineDatesExempt === "yes" ? true : false,
    };

    if (data.vaccineDatesExempt === "yes") {
      newData = {
        ...newData,
        vaccineProducer: null,
        vaccineBrand: null,
        rabiesTagNumber: null,
        vaccineAdministeredDate: null,
        vaccineDueDate: null,
        vaccineLotNumber: null,
        vaccineLotExpirationDate: null,
        dogRabiesVaccinationDocument: null,
      };
    }

    const formData = createFormData(newData);

    updateEntity.mutate({
      entityId: entity.entityId,
      body: formData,
    });
  };

  const vaccineDatesWatch = watchField("vaccineDatesExempt") === "yes";
  const { hasPermissions } = useMyProfile();

  return (
    <ProfileSection
      label="Rabies Vaccination Information"
      onSubmit={onSubmit}
      saving={updateEntity.isLoading}
      hideEdit={!currentEntity.active}
    >
      <ProfileGrid cols={1}>
        <ProfileGridItem
          label="Veterinary Name"
          flagged={rejectedFields.includes("veterinaryName")}
          field={"veterinaryName"}
          required
        >
          <ProfileInput name="veterinaryName" required />
        </ProfileGridItem>
      </ProfileGrid>

      {!vaccineDatesWatch && (
        <>
          <ProfileGrid cols={3}>
            <ProfileGridItem
              label="Vaccine Producer"
              flagged={rejectedFields.includes("vaccineProducer")}
              field={"vaccineProducer"}
              required={!hasPermissions(["super-admin"])}
            >
              {rabiesProducersIsLoading
                ? "Loading..."
                : rabiesProducers && (
                    <ProfileCustomSelectInput
                      name="vaccineProducer"
                      options={rabiesProducers}
                      required={!hasPermissions(["super-admin"])}
                    />
                  )}
            </ProfileGridItem>

            <ProfileGridItem
              label="Vaccine Brand"
              flagged={rejectedFields.includes("vaccineBrand")}
              field={"vaccineBrand"}
              required={!hasPermissions(["super-admin"])}
            >
              {rabiesBrandsIsLoading
                ? "Loading..."
                : rabiesBrands && (
                    <ProfileCustomSelectInput
                      name="vaccineBrand"
                      options={rabiesBrands}
                      required={!hasPermissions(["super-admin"])}
                    />
                  )}
            </ProfileGridItem>

            <ProfileGridItem
              label="Rabies Tag Number"
              flagged={rejectedFields.includes("rabiesTagNumber")}
              field={"rabiesTagNumber"}
              required={!hasPermissions(["super-admin"])}
            >
              <ProfileInput name="rabiesTagNumber" 
                required={!hasPermissions(["super-admin"])}
              />
            </ProfileGridItem>
          </ProfileGrid>

          <ProfileGrid cols={3}>
            <ProfileGridItem
              label="Vaccine Administered Date"
              flagged={rejectedFields.includes("vaccineAdministeredDate")}
              field={"vaccineAdministeredDate"}
              required
            >
              <ProfileDateInput name="vaccineAdministeredDate" required />
            </ProfileGridItem>

            <ProfileGridItem
              label="Vaccine Due Date"
              flagged={rejectedFields.includes("vaccineDueDate")}
              field={"vaccineDueDate"}
              required
            >
              <ProfileDateInput name="vaccineDueDate" required />
            </ProfileGridItem>
          </ProfileGrid>

          <ProfileGrid cols={3}>
            <ProfileGridItem
              label="Lot Number"
              flagged={rejectedFields.includes("vaccineLotNumber")}
              field={"vaccineLotNumber"}
            >
              <ProfileInput name="vaccineLotNumber" />
            </ProfileGridItem>

            <ProfileGridItem
              label="Lot Expiration Date"
              flagged={rejectedFields.includes("vaccineLotExpirationDate")}
              field={"vaccineLotExpirationDate"}
            >
              <ProfileDateInput name="vaccineLotExpirationDate" />
            </ProfileGridItem>
          </ProfileGrid>
        </>
      )}

      <ProfileGrid cols={3}>
        <ProfileGridItem
          label="Vaccine Dates Exempt"
          flagged={rejectedFields.includes("vaccineDatesExempt")}
          field={"vaccineDatesExempt"}
          required
        >
          <ProfileSelectInput
            name="vaccineDatesExempt"
            options={[
              { label: "Yes", value: "yes" },
              { label: "No", value: "no" },
            ]}
            required
          />
        </ProfileGridItem>
      </ProfileGrid>
      <ProfileGrid cols={2}>
        {/* Rabies Document */}
        {!vaccineDatesWatch && (
          <ProfileGridItem
            label="Rabies Document"
            flagged={rejectedFields.includes("dogRabiesVaccinationDocument")}
            field={"dogRabiesVaccinationDocument"}
            required={!hasPermissions(["super-admin"])}
          >
            <ProfileDocument
              name="dogRabiesVaccinationDocument"
              accept={[".pdf", ".jpeg", ".png", ".jpg"]}
              required={!hasPermissions(["super-admin"])}
            />
          </ProfileGridItem>
        )}

        {/* Rabies Exemption */}
        {vaccineDatesWatch && (
          <ProfileGridItem
            label="Rabies Exemption Document"
            flagged={rejectedFields.includes(
              "dogRabiesVaccinationExemptionDocument",
            )}
            field={"dogRabiesVaccinationExemptionDocument"}
            required={!hasPermissions(["super-admin"])}
          >
            <ProfileDocument
              name="dogRabiesVaccinationExemptionDocument"
              accept={[".pdf", ".jpeg", ".png", ".jpg"]}
              required={!hasPermissions(["super-admin"])}
            />
          </ProfileGridItem>
        )}
      </ProfileGrid>
    </ProfileSection>
  );
};
