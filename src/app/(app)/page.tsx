"use client";

import { useEffect, useRef } from "react";
import Link from "next/link";

import Footer from "@/components/landingPage/Footer";
import Features from "@/components/landingPage/Features";
import Navbar from "@/components/landingPage/Navbar";
import Solutions from "@/components/landingPage/Solutions";
import AboutSection from "@/components/landingPage/AboutUs";
import Hero from "@/components/landingPage/Hero";

// shadcn/ui imports
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";

const LandingPage = () => {
  const accessibilityMenuRef = useRef(null);

  // On page load, check for a hash and scroll into view if present.
  useEffect(() => {
    if (typeof window !== "undefined" && window.location.hash) {
      const hash = window.location.hash.substring(1);
      const el = document.getElementById(hash);
      if (el) {
        el.scrollIntoView({ behavior: "smooth" });
        // Remove focus from any element after scrolling
        setTimeout(() => {
          if (document.activeElement instanceof HTMLElement) {
            document.activeElement.blur();
          }
        }, 100);
      }
    }
  }, []);

  // Function to scroll to element and remove focus
  const scrollToElementAndBlur = (id: string) => {
    const el = document.getElementById(id);
    if (el) {
      el.scrollIntoView({ behavior: "smooth" });
      // Remove focus after scrolling
      setTimeout(() => {
        if (document.activeElement instanceof HTMLElement) {
          document.activeElement.blur();
        }
      }, 100);
    }
  };

  // Listen for keyboard shortcuts.
  useEffect(() => {
    function handleKeyDown(e: KeyboardEvent) {
      if (e.altKey && e.key === "/") {
        e.preventDefault();
        const searchInput = document.getElementById("search-input");
        if (searchInput) {
          searchInput.focus();
        }
      }
      // Shift + Alt + H: Navigate to Home.
      else if (e.shiftKey && e.altKey && e.key.toLowerCase() === "h") {
        e.preventDefault();
        scrollToElementAndBlur("home");
      }
      // Shift + Alt + F: Navigate to Features.
      else if (e.shiftKey && e.altKey && e.key.toLowerCase() === "f") {
        e.preventDefault();
        scrollToElementAndBlur("features");
      }
      // Shift + Alt + S: Navigate to Solutions.
      else if (e.shiftKey && e.altKey && e.key.toLowerCase() === "s") {
        e.preventDefault();
        scrollToElementAndBlur("solutions");
      }
      // Shift + Alt + A: Navigate to About.
      else if (e.shiftKey && e.altKey && e.key.toLowerCase() === "a") {
        e.preventDefault();
        scrollToElementAndBlur("about");
      }
    }

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, []);

  return (
    <div className="relative min-h-screen bg-white overflow-x-hidden" id="landing-page">
      <div
        ref={accessibilityMenuRef}
        className={cn(
          "absolute left-0 top-0 z-[9999] -translate-y-full transition-transform duration-300 focus-within:left-2 focus-within:top-2 focus-within:translate-y-0 focus-within:outline-none",
        )}
        // style={{
        //   transform: "translateY(-100%)",
        // }}
      >
        <Link
          href="#main-content"
          className={cn(buttonVariants({ variant: "outline" }))}
          onClick={(e) => {
            // Delay to ensure the navigation happens first
            setTimeout(() => {
              if (document.activeElement instanceof HTMLElement) {
                document.activeElement.blur();
              }
            }, 100);
          }}
        >
          Skip to main content
        </Link>
        <div className="mt-2 rounded-lg border border-gray-200 bg-white p-3">
          <h2 className="text-sm font-semibold text-gray-800">
            Keyboard Shortcuts
          </h2>
          <ul className="mt-2 space-y-1.5 text-xs">
            <li className="flex items-center">
              <kbd className="mr-2 rounded-md border border-gray-200 bg-gray-100 px-1.5 py-0.5 text-gray-700 shadow-sm">
                Shift + Alt + H
              </kbd>
              <span className="text-gray-600">Navigate to Home</span>
            </li>
            <li className="flex items-center">
              <kbd className="mr-2 rounded-md border border-gray-200 bg-gray-100 px-1.5 py-0.5 text-gray-700 shadow-sm">
                Shift + Alt + F
              </kbd>
              <span className="text-gray-600">Navigate to Features</span>
            </li>
            <li className="flex items-center">
              <kbd className="mr-2 rounded-md border border-gray-200 bg-gray-100 px-1.5 py-0.5 text-gray-700 shadow-sm">
                Shift + Alt + S
              </kbd>
              <span className="text-gray-600">Navigate to Solutions</span>
            </li>
            <li className="flex items-center">
              <kbd className="mr-2 rounded-md border border-gray-200 bg-gray-100 px-1.5 py-0.5 text-gray-700 shadow-sm">
                Shift + Alt + A
              </kbd>
              <span className="text-gray-600">Navigate to About</span>
            </li>
          </ul>
        </div>
      </div>

      <Navbar />

      <main id="main-content">
        <Hero />
      </main>
      <Footer />
    </div>
  );
};

export default LandingPage;
