"use client";
import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent } from "@/components/ui/card";
import { Calendar, Mail, MapPin } from "lucide-react";
import Footer from "@/components/landingPage/Footer";
import SalesNavbar from "@/components/landingPage/sales/SalesNavbar";
import { useSendEmailDemo } from "@/hooks/api/useRealm";
import Confetti from "react-confetti";
import { use<PERSON><PERSON> } from "jotai";
import { toast<PERSON>tom } from "@/components/ui/toast/toast";

const populationSizes = [
  { value: "under5k", label: "Under 5,000" },
  { value: "5kTo15k", label: "5,000 - 15,000" },
  { value: "15kTo50k", label: "15,000 - 50,000" },
  { value: "50kTo100k", label: "50,000 - 100,000" },
  { value: "over100k", label: "Over 100,000" },
];

const services = [
  {
    id: "Permit & Licensing Management",
    label: "Permit & Licensing Management",
  },
  {
    id: "Vital Records",
    label: "Vital Records (Birth, Death, Marriage Certificates)",
  },
  {
    id: "Dog/Pet Licensing",
    label: "Dog/Pet Licensing",
  },
  {
    id: "Business Registration",
    label: "Business Registration",
  },
  {
    id: "Public Records Requests",
    label: "Public Records Requests",
  },
  {
    id: "Online Payment Processing",
    label: "Online Payment Processing",
  },
  {
    id: "Document Management",
    label: "Document Management",
  },
  {
    id: "Citizen Self-Service Portal",
    label: "Citizen Self-Service Portal",
  },
];

// Define form values interface
interface FormValues {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  jobTitle: string;
  cityName: string;
  state: string;
  populationSize: string;
  services: string[];
  additionalInfo: string;
}

const DemoPage = () => {
  useEffect(() => {
    if (typeof window !== "undefined" && window.location.hash) {
      const hash = window.location.hash.substring(1);
      const el = document.getElementById(hash);
      if (el) {
        el.scrollIntoView({ behavior: "smooth" });
      }
    }
  }, []);

  const sendEmail = useSendEmailDemo();

  const form = useForm<FormValues>({
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      jobTitle: "",
      cityName: "",
      state: "",
      populationSize: "",
      services: [],
      additionalInfo: "",
    },
  });

  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [showConfetti, setShowConfetti] = useState(false);
  const [_, setToast] = useAtom(toastAtom);

  useEffect(() => {
    if (showConfetti) {
      const timer = setTimeout(() => {
        setShowConfetti(false);
      }, 10000);
      return () => clearTimeout(timer);
    }
  }, [showConfetti]);

  function onSubmit(values: FormValues) {
    const emailData = {
      ...values,
      services: values.services.join(", "),
    };
    sendEmail.mutate(emailData, {
      onSuccess: () => {
        setSuccessMessage(
          "Demo request submitted successfully! We'll contact you soon.",
        );
        setShowConfetti(true);
        form.reset();
      },
      onError: (error) => {
        console.error("Error sending email:", error);
        setToast({
          label: "Error",
          message:
            "There was an error submitting your demo request. Please try again.",
          status: "error",
        });
      },
    });
  }

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 },
    },
  };

  return (
    <div className="flex flex-col" id="demo">
      {showConfetti && (
        <Confetti
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
          }}
        />
      )}
      <SalesNavbar />
      <section id="contact" className="py-32">
        <div className="container mx-auto px-4">
          {/* Section Header */}
          <div className="mb-16 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              <span className="rounded-full bg-black px-4 py-1 text-sm font-medium text-white">
                Get Started
              </span>
            </motion.div>
            <motion.h2
              className="mt-6 text-4xl font-bold md:text-5xl"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Request a personalized demo
            </motion.h2>
            <motion.p
              className="mx-auto mt-4 max-w-2xl text-lg text-gray-600"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              See how ClerkXpress can transform your city&apos;s operations with
              a customized demonstration tailored to your specific needs.
            </motion.p>
          </div>

          <div className="grid gap-10 lg:grid-cols-3">
            {/* Contact Information */}
            <div className="lg:col-span-1">
              <motion.div
                variants={fadeIn}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                className="space-y-8"
              >
                <div>
                  <h3 className="mb-4 text-2xl font-bold">Contact us</h3>
                  <p className="text-gray-600">
                    Have questions before scheduling a demo? Reach out to our
                    team directly and we&apos;ll be happy to help.
                  </p>
                </div>

                {/* Podcast Player */}
                <div className="mb-8 mt-6">
                  <h4 className="mb-3 text-lg font-semibold">
                    Listen to our podcast
                  </h4>
                  <div className="rounded-lg border border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 p-4">
                    <div className="flex items-center gap-4">
                      <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-blue-600">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="white"
                          className="h-6 w-6"
                        >
                          <path d="M8.25 4.5a3.75 3.75 0 117.5 0v8.25a3.75 3.75 0 11-7.5 0V4.5z" />
                          <path d="M6 10.5a.75.75 0 01.75.75v1.5a5.25 5.25 0 1010.5 0v-1.5a.75.75 0 011.5 0v1.5a6.751 6.751 0 01-6 6.709v2.291h3a.75.75 0 010 1.5h-7.5a.75.75 0 010-1.5h3v-2.291a6.751 6.751 0 01-6-6.709v-1.5A.75.75 0 016 10.5z" />
                        </svg>
                      </div>
                      <div className="flex-grow">
                        <h5 className="font-medium text-gray-900">
                          ClerkXpress Explainer
                        </h5>
                        <p className="mb-2 text-sm text-gray-600">
                          Learn how our platform transforms municipal operations
                        </p>
                        <audio
                          className="w-full"
                          controls
                          src="/audio/clerkpodcast.wav"
                        >
                          Your browser does not support the audio element.
                        </audio>
                      </div>
                    </div>
                  </div>
                </div>

                <Card>
                  <CardContent className="pt-6">
                    <div className="space-y-4">
                      <div className="flex items-start">
                        <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                          <Mail className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium">Email</p>
                          <p className="text-gray-600"><EMAIL></p>
                        </div>
                      </div>

                      {/* <div className="flex items-start">
                      <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                        <Phone className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium">Phone</p>
                        <p className="text-gray-600">(*************</p>
                      </div>
                    </div> */}

                      <div className="flex items-start">
                        <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                          <MapPin className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium">Office</p>
                          <p className="text-gray-600">
                            1462 Erie Blvd
                            <br />
                            Suite C101
                            <br />
                            Schenectady, NY 12305
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start">
                        <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                          <Calendar className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium">Business Hours</p>
                          <p className="text-gray-600">
                            Monday - Friday: 9am - 5pm EST
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <div>
                  <h4 className="mb-2 text-lg font-semibold">What to expect</h4>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 flex-shrink-0 text-blue-600">
                        <svg
                          className="h-4 w-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <span>A response within 1 business day</span>
                    </li>
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 flex-shrink-0 text-blue-600">
                        <svg
                          className="h-4 w-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <span>
                        A custom demo tailored to your city&apos;s needs
                      </span>
                    </li>
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 flex-shrink-0 text-blue-600">
                        <svg
                          className="h-4 w-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <span>Transparent pricing information</span>
                    </li>
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 flex-shrink-0 text-blue-600">
                        <svg
                          className="h-4 w-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <span>No pressure or obligation</span>
                    </li>
                  </ul>
                </div>
              </motion.div>
            </div>

            {/* Contact Form */}
            <motion.div
              variants={fadeIn}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
              className="lg:col-span-2"
            >
              <Card className="overflow-hidden">
                <CardContent className="p-6 sm:p-8">
                  <Form {...form}>
                    <form
                      onSubmit={form.handleSubmit(onSubmit)}
                      className="space-y-6"
                    >
                      <div className="grid gap-6 md:grid-cols-2">
                        {/* Contact Information Section */}
                        <div className="md:col-span-2">
                          <h3 className="mb-4 text-xl font-semibold">
                            Your Information
                          </h3>
                        </div>

                        <FormField
                          control={form.control}
                          name="firstName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>First name</FormLabel>
                              <FormControl>
                                <Input placeholder="John" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                          rules={{
                            required: "First name is required",
                            minLength: {
                              value: 2,
                              message:
                                "First name must be at least 2 characters",
                            },
                          }}
                        />

                        <FormField
                          control={form.control}
                          name="lastName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Last name</FormLabel>
                              <FormControl>
                                <Input placeholder="Doe" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                          rules={{
                            required: "Last name is required",
                            minLength: {
                              value: 2,
                              message:
                                "Last name must be at least 2 characters",
                            },
                          }}
                        />

                        <FormField
                          control={form.control}
                          name="email"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Email</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="<EMAIL>"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                          rules={{
                            required: "Email is required",
                            pattern: {
                              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                              message: "Please enter a valid email address",
                            },
                          }}
                        />

                        <FormField
                          control={form.control}
                          name="phone"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Phone number</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="(*************"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                          rules={{
                            required: "Phone number is required",
                            minLength: {
                              value: 10,
                              message: "Please enter a valid phone number",
                            },
                          }}
                        />

                        <FormField
                          control={form.control}
                          name="jobTitle"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Job title</FormLabel>
                              <FormControl>
                                <Input placeholder="City Clerk" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                          rules={{
                            required: "Job title is required",
                          }}
                        />

                        {/* Municipality Information Section */}
                        <div className="md:col-span-2">
                          <h3 className="mb-4 mt-4 text-xl font-semibold">
                            Municipality Information
                          </h3>
                        </div>

                        <FormField
                          control={form.control}
                          name="cityName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>City/Town name</FormLabel>
                              <FormControl>
                                <Input placeholder="Cityville" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                          rules={{
                            required: "City/Town name is required",
                          }}
                        />

                        <FormField
                          control={form.control}
                          name="state"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>State</FormLabel>
                              <FormControl>
                                <Input placeholder="New York" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                          rules={{
                            required: "State is required",
                          }}
                        />

                        <FormField
                          control={form.control}
                          name="populationSize"
                          render={({ field }) => (
                            <FormItem className="md:col-span-2">
                              <FormLabel>Population size</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select population size" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {populationSizes.map((size) => (
                                    <SelectItem
                                      key={size.value}
                                      value={size.value}
                                    >
                                      {size.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                          rules={{
                            required:
                              "Please select your city's population size",
                          }}
                        />

                        {/* Services Section */}
                        <div className="md:col-span-2">
                          <h3 className="mb-4 mt-4 text-xl font-semibold">
                            Services of Interest
                          </h3>
                          <FormField
                            control={form.control}
                            name="services"
                            render={() => (
                              <FormItem>
                                <div className="grid gap-2 md:grid-cols-2">
                                  {services.map((service) => (
                                    <FormField
                                      key={service.id}
                                      control={form.control}
                                      name="services"
                                      render={({ field }) => {
                                        return (
                                          <FormItem
                                            key={service.id}
                                            className="flex flex-row items-start space-x-3 space-y-0"
                                          >
                                            <FormControl>
                                              <Checkbox
                                                checked={(
                                                  field.value as string[]
                                                ).includes(service.id)}
                                                onCheckedChange={(checked) => {
                                                  const currentValues =
                                                    field.value as string[];
                                                  return checked
                                                    ? field.onChange([
                                                        ...currentValues,
                                                        service.id,
                                                      ])
                                                    : field.onChange(
                                                        currentValues.filter(
                                                          (value) =>
                                                            value !==
                                                            service.id,
                                                        ),
                                                      );
                                                }}
                                              />
                                            </FormControl>
                                            <FormLabel className="font-normal">
                                              {service.label}
                                            </FormLabel>
                                          </FormItem>
                                        );
                                      }}
                                      rules={{
                                        validate: (value) =>
                                          (value as string[]).length > 0 ||
                                          "Please select at least one service",
                                      }}
                                    />
                                  ))}
                                </div>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <FormField
                          control={form.control}
                          name="additionalInfo"
                          render={({ field }) => (
                            <FormItem className="md:col-span-2">
                              <FormLabel>Additional information</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Tell us about your current processes and challenges..."
                                  {...field}
                                  rows={4}
                                />
                              </FormControl>
                              <FormDescription>
                                Share any specific needs or questions you have.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="md:col-span-2">
                          <Button
                            type="submit"
                            className="w-full bg-blue-600 hover:bg-blue-700"
                            disabled={form.formState.isSubmitting}
                          >
                            Request Demo
                          </Button>
                        </div>
                        {successMessage && (
                          <div className="mt-4 text-green-600">
                            {successMessage}
                          </div>
                        )}
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>
      <Footer />
    </div>
  );
};

export default DemoPage;
