import { useLicenseListContext, useCurrentLicenseContext } from "@/components/approval/hooks/useDogLicenseApproval";
import { cn } from "@/lib/utils";
import { Dog } from "@/types/DogType";
import { formatDistanceToNow } from "date-fns";
import {
  DogIcon,
  UserCircle,
  Clock,
  Award,
  Tag,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { usePathname, useSearchParams, useRouter } from "next/navigation";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";

export const DogLicenseApprovalSidebar = () => {
  const { replace } = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const licenseId = searchParams.get("licenseId");
  const {
    loadingLicenseList,
    errorLicenseList,
    licenseList,
    // Pagination
    currentPage,
    totalPages,
    totalElements,
    hasNextPage,
    hasPrevPage,
    goToNextPage,
    goToPrevPage,
  } = useLicenseListContext();
  
  const { refetchCurrentLicense } = useCurrentLicenseContext();

  useEffect(() => {
    if (licenseList && licenseList.length > 0 && licenseList[0].license?.entityId) {
      // Check if current licenseId exists in current page's license list
      const currentLicenseExists = licenseList.some(
        (item) => item.license?.entityId === licenseId
      );
      
      // If no licenseId or current license not found in current page, navigate to first license of this page
      if (!licenseId || !currentLicenseExists) {
        const params = new URLSearchParams(searchParams.toString());
        params.set("licenseId", licenseList[0].license.entityId);
        // Preserve current page and size
        params.set("page", currentPage.toString());
        params.set("size", "8");
        replace(`${pathname}?${params.toString()}`);
      }
    }
  }, [licenseList, licenseId, pathname, replace, searchParams, currentPage]);

  if (loadingLicenseList) return <div>Loading...</div>;
  if (errorLicenseList) return <div>Error</div>;

  return (
    <>
      <div className="flex h-full flex-col overflow-y-auto overflow-x-hidden">
        {/* License List */}
        {licenseList && licenseList.length > 0 ? (
          <div className="space-y-2 p-1">
            {licenseList.map((item: any) => {
              const { license, individual, dog } = item;

              if (!license || !individual) {
                return null;
              }

              const fullname = `${individual[0]?.firstName} ${individual[0].lastName}`;
              const active = license.entityId === searchParams.get("licenseId");
              const modifiedDate = license.applicationDate
                ? formatDistanceToNow(new Date(license.applicationDate), {
                    addSuffix: true,
                  })
                : "N/A";

              const containsPend = license.licenseNumber.includes("PEND");

              return (
                <div
                  key={license.entityId}
                  onClick={() => {
                    const params = new URLSearchParams(searchParams.toString());
                    params.set("licenseId", license.entityId);
                    replace(`${pathname}?${params.toString()}`);
                    refetchCurrentLicense();
                  }}
                  className={cn(
                    "group relative cursor-pointer transition-all duration-300 ease-out",
                    "border border-slate-200/60 bg-white hover:border-slate-300 hover:bg-slate-50/80",
                    "rounded-lg shadow-sm hover:rounded-xl hover:shadow-md hover:shadow-slate-200/40",
                    "transform hover:-translate-y-0.5",
                    active &&
                      "border-blue-500/60 bg-blue-50/70 shadow-blue-500 ring-2 ring-blue-500",
                  )}
                >
                  <div className="space-y-2.5 p-2">
                    {/* Header with license number and status badge */}
                    <div className="flex items-start justify-between gap-2">
                      <div className="min-w-0 flex-1">
                        <div className="mb-0.5 flex items-center gap-1.5">
                          <Award className="size-3.5 flex-shrink-0 text-blue-600" />
                          <h3 className="truncate text-sm font-bold tracking-tight text-blue-900">
                            {license.licenseNumber}
                          </h3>
                        </div>
                        <div className="flex items-center gap-1 text-xs text-slate-500">
                          <Clock className="size-2.5" />
                          <span className="font-medium">{modifiedDate}</span>
                        </div>
                      </div>

                      <div className="flex flex-col items-end gap-1">
                        <span
                          className={cn(
                            "inline-flex items-center rounded-full px-2 py-0.5 text-xs font-semibold",
                            "border shadow-sm transition-colors",
                            containsPend
                              ? "border-emerald-600/20 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white"
                              : "border-blue-600/20 bg-gradient-to-r from-blue-500 to-blue-600 text-white",
                          )}
                        >
                          {containsPend ? "New" : "Renewal"}
                        </span>
                        {/* <span className="rounded-full bg-slate-100 px-1.5 py-0.5 text-xs font-medium text-slate-600">
                          {license.licenseDuration}Y
                        </span> */}
                      </div>
                    </div>

                    {/* Dogs section */}
                    <div className="">
                      {dog.map((dog: Dog, index: number) => (
                        <div
                          key={dog.entityId}
                          className="rounded-md border border-slate-100 bg-slate-50/70 transition-colors group-hover:bg-white/80"
                        >
                          <div className="flex items-center gap-1">
                            <div className="rounded-md bg-blue-100 p-1">
                              <DogIcon className="size-3 text-blue-600" />
                            </div>
                            <div className="line-clamp-1 text-sm font-bold tracking-tight text-slate-900">
                              {dog.dogName}
                            </div>
                            <div className="ml-6 flex items-center gap-1.5">
                              <Tag className="size-2.5 text-slate-400" />
                              <span
                                className={cn(
                                  "text-xs font-medium",
                                  dog?.tagNumber
                                    ? "text-slate-600"
                                    : "italic text-slate-400",
                                )}
                              >
                                {dog?.tagNumber
                                  ? `#${dog.tagNumber}`
                                  : "No tag"}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* License holder section */}
                    <div className="border-t border-slate-100">
                      <div className="flex items-center gap-1.5">
                        <UserCircle className="size-3 text-slate-400" />
                        <p className="line-clamp-1 text-xs font-semibold text-slate-700">
                          {fullname}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Subtle gradient overlay on hover */}
                  <div className="pointer-events-none absolute inset-0 rounded-lg bg-gradient-to-br from-blue-50/0 via-transparent to-slate-50/0 transition-all duration-300 group-hover:from-blue-50/30 group-hover:to-slate-50/10" />
                </div>
              );
            })}
          </div>
        ) : (
          <div className="p-2 text-center text-sm text-neutral-500">
            No licenses found
          </div>
        )}
      </div>

      {/* Pagination Controls */}
      <div className="mt-auto flex shrink-0 flex-col border-t border-slate-200 bg-white p-3">
        <div className="mb-2 flex items-center justify-between">
          <div className="text-xs text-slate-500">
            Page {currentPage + 1} of {Math.max(totalPages, 1)}
          </div>
          <div className="text-xs text-slate-500">{totalElements} total</div>
        </div>
        <div className="flex gap-1">
          <Button
            onClick={goToPrevPage}
            disabled={!hasPrevPage || loadingLicenseList}
            variant="outline"
            size="sm"
            className="h-8 flex-1 text-xs"
          >
            <ChevronLeft className="mr-1 size-3" />
            Previous
          </Button>
          <Button
            onClick={goToNextPage}
            disabled={!hasNextPage || loadingLicenseList}
            variant="outline"
            size="sm"
            className="h-8 flex-1 text-xs"
          >
            Next
            <ChevronRight className="ml-1 size-3" />
          </Button>
        </div>
      </div>
    </>
  );
};
