"use client";
import SearchLayout from "@/components/searchBuilder/SearchLayout";
import { dogLicenseSearchForm } from "@/lib/configuration/dogLicenseSearchForm";
import individualSearchForm from "@/lib/configuration/individualSearchForm";
import { useParams } from "next/navigation";

const SearchPage = () => {
  const params = useParams();
  const entityType = params.entityType as string;

  const getSearchForm = (entityType: string) => {
    switch (entityType) {
      case "individual":
        return individualSearchForm;
      case "dog":
        return dogLicenseSearchForm;
      default:
        return null;
    }
  };

  if (entityType) {
    return (
      <div className="min-h-full overflow-y-auto overflow-x-hidden bg-neutral-100">
        {getSearchForm(entityType) ? (
          <SearchLayout formData={getSearchForm(entityType)!} />
        ) : null}
      </div>
    );
  } else {
    return (
      <div className="flex h-screen items-center justify-center">
        No entity type provided
      </div>
    );
  }
};

export default SearchPage;
