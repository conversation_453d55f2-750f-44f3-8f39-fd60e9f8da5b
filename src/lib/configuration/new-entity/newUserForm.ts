export const newUserForm = {
  name: "newUserForm",
  description: "Form used to create a new user/resident",
  pages: [
    {
      title: "User Information",
      optional: false,
      sortOrder: 3,
      conditionallyDisplay: [],
      sections: [
        {
          title: "Personal Information",
          sortOrder: 1,
          conditionallyDisplay: [],
          elements: [
            {
              label: "Title",
              description: null,
              fieldName: "title",
              type: "select",
              defaultValue: "",
              sortOrder: 1,
              size: "xs",
              info: null,
              google: false,
              required: {
                value: false,
                message: "",
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [
                {
                  label: "Select",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Mr",
                  value: "Mr",
                  sortOrder: 2,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Mrs",
                  value: "Mrs",
                  sortOrder: 3,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Miss",
                  value: "Miss",
                  sortOrder: 4,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Ms",
                  value: "Ms",
                  sortOrder: 5,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Dr",
                  value: "Dr",
                  sortOrder: 6,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Prof",
                  value: "Prof",
                  sortOrder: 7,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Rev",
                  value: "Rev",
                  sortOrder: 8,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Hon",
                  value: "Hon",
                  sortOrder: 9,
                  description: null,
                  group: null,
                  default: false,
                },
              ],
              columnOrCustomFieldName: "title",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "First Name",
              description: null,
              fieldName: "firstName",
              type: "text",
              defaultValue: "",
              sortOrder: 2,
              size: "sm",
              info: null,
              google: false,
              required: {
                value: true,
                message: "First name is required",
              },
              minLength: {
                value: 1,
                message: "First name must be at least 1 characters",
              },
              minValue: null,
              maxLength: {
                value: 100,
                message: "First name must be at most 255 characters",
              },
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "firstName",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [
                {
                  tableName: "participant",
                  columnName: "participant_type_group_id",
                  value: 1,
                },
              ],
            },
            {
              label: "Middle Name",
              description: null,
              fieldName: "middleName",
              type: "text",
              defaultValue: "",
              sortOrder: 3,
              size: "sm",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "middleName",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Last Name",
              description: null,
              fieldName: "lastName",
              type: "text",
              defaultValue: "",
              sortOrder: 4,
              size: "sm",
              info: null,
              google: false,
              required: {
                value: true,
                message: "",
              },
              minLength: {
                value: 1,
                message: "Last name must be at least 1 characters",
              },
              minValue: null,
              maxLength: {
                value: 100,
                message: "Last name must be at most 255 characters",
              },
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "lastName",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Suffix",
              description: null,
              fieldName: "suffix",
              type: "select",
              defaultValue: "",
              sortOrder: 5,
              size: "xs",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [
                {
                  label: "Select",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Jr",
                  value: "Jr",
                  sortOrder: 2,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Sr",
                  value: "Sr",
                  sortOrder: 3,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "II",
                  value: "II",
                  sortOrder: 4,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "III",
                  value: "III",
                  sortOrder: 5,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "IV",
                  value: "IV",
                  sortOrder: 6,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "PhD",
                  value: "PhD",
                  sortOrder: 7,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "MD",
                  value: "MD",
                  sortOrder: 8,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "DDS",
                  value: "DDS",
                  sortOrder: 9,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Esq",
                  value: "Esq",
                  sortOrder: 10,
                  description: null,
                  group: null,
                  default: false,
                },
              ],
              columnOrCustomFieldName: "suffix",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Email",
              description: null,
              fieldName: "email",
              type: "email",
              defaultValue: "",
              sortOrder: 6,
              size: "md",
              info: null,
              google: false,
              required: {
                value: true,
                message: "",
              },
              minLength: {
                value: 1,
                message: "Email must be at least 1 characters",
              },
              minValue: null,
              maxLength: {
                value: 100,
                message: "Email must be at most 100 characters",
              },
              maxValue: null,
              pattern: {
                value: "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$",
                message: "Email must be a valid email address",
              },
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "contact_value",
              tableName: "contact",
              argumentTemplate: null,
              arguments: [
                {
                  tableName: "contact",
                  columnName: "contact_type_group_id",
                  value: 1,
                },
              ],
            },
            {
              label: "Phone",
              description: null,
              fieldName: "phone",
              type: "tel",
              defaultValue: "",
              sortOrder: 7,
              size: "md",
              info: null,
              google: false,
              required: {
                value: true,
                message: "",
              },
              minLength: {
                value: 1,
                message: "Phone must be at least 1 characters",
              },
              minValue: null,
              maxLength: {
                value: 15,
                message: "Phone must be at most 10 characters",
              },
              maxValue: null,
              pattern: {
                value: "^\\(\\d{3}\\) \\d{3}-\\d{4}$",
                message: "Phone must be a valid phone number",
              },
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "contact_value",
              tableName: "contact",
              argumentTemplate: null,
              arguments: [
                {
                  tableName: "contact",
                  columnName: "contact_type_group_id",
                  value: 5,
                },
              ],
            },
            {
              label: "Date of Birth",
              description: null,
              fieldName: "dateOfBirth",
              type: "date",
              defaultValue: "",
              sortOrder: 8,
              size: "md",
              info: null,
              google: false,
              required: {
                value: true,
                message: "",
              },
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: {
                value: "currentDate",
                message: "Date of Birth must a valid date",
              },
              pattern: {
                value:
                  "^(19|20)\\d{2}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$",
                message: "Date of Birth must a valid date",
              },
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "dateOfBirth",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
        {
          title: "Primary Residency",
          sortOrder: 2,
          conditionallyDisplay: [],
          elements: [
            {
              label: "Address",
              description: null,
              fieldName: "address",
              type: "text",
              defaultValue: "",
              sortOrder: 1,
              size: "xl",
              info: null,
              google: true,
              required: {
                value: true,
                message: "Address is required",
              },
              minLength: null,
              minValue: null,
              maxLength: {
                value: 255,
                message: "Address must be at most 255 characters",
              },
              maxValue: null,
              pattern: {
                value: "^[a-zA-Z0-9\\s\\-\\#\\.\\,\\']+$",
                message: "Address must be a valid address",
              },
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "street_address",
              tableName: "address",
              argumentTemplate: null,
              arguments: [
                {
                  tableName: "participant_address_type",
                  columnName: "participant_address_type_id",
                  value: 5,
                },
              ],
            },
            {
              label: "Address 2",
              description: null,
              fieldName: "address2",
              type: "text",
              defaultValue: "",
              sortOrder: 2,
              size: "md",
              info: null,
              google: true,
              required: {
                value: false,
                message: "",
              },
              minLength: null,
              minValue: null,
              maxLength: {
                value: 255,
                message: "Address 2 must be at most 255 characters",
              },
              maxValue: null,
              pattern: {
                value: "^[a-zA-Z0-9\\s\\-\\#\\.\\,\\']+$",
                message: "Address 2 must be a valid address",
              },
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "street_address_2",
              tableName: "address",
              argumentTemplate: null,
              arguments: [
                {
                  tableName: "participant_address_type",
                  columnName: "participant_address_type_id",
                  value: 5,
                },
              ],
            },
            {
              label: "City",
              description: null,
              fieldName: "city",
              type: "text",
              defaultValue: "Schenectady",
              sortOrder: 3,
              size: "lg",
              info: null,
              google: true,
              required: {
                value: true,
                message: "City is required",
              },
              minLength: null,
              minValue: null,
              maxLength: {
                value: 255,
                message: "City must be at most 255 characters",
              },
              maxValue: null,
              pattern: {
                value: "^[a-zA-Z\\s\\-\\']+$",
                message: "City must be a valid city",
              },
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "city",
              tableName: "address",
              argumentTemplate: null,
              arguments: [
                {
                  tableName: "participant_address_type",
                  columnName: "participant_address_type_id",
                  value: 5,
                },
              ],
            },
            {
              label: "State",
              description: null,
              fieldName: "state",
              type: "select",
              defaultValue: "NY",
              sortOrder: 4,
              size: "sm",
              info: null,
              google: true,
              required: {
                value: true,
                message: "State is required",
              },
              minLength: null,
              minValue: null,
              maxLength: {
                value: 2,
                message: "State must be at most 2 characters",
              },
              maxValue: null,
              pattern: {
                value: "^[a-zA-Z]{2}$",
                message: "State must be a-z or A-Z",
              },
              conditionallyDisplay: [],
              triggers: [],
              options: [
                {
                  label: "Select a State",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Alabama",
                  value: "AL",
                  sortOrder: 2,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Alaska",
                  value: "AK",
                  sortOrder: 3,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Arizona",
                  value: "AZ",
                  sortOrder: 4,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Arkansas",
                  value: "AR",
                  sortOrder: 5,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "California",
                  value: "CA",
                  sortOrder: 6,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Colorado",
                  value: "CO",
                  sortOrder: 7,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Connecticut",
                  value: "CT",
                  sortOrder: 8,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Delaware",
                  value: "DE",
                  sortOrder: 9,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "District Of Columbia",
                  value: "DC",
                  sortOrder: 10,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Florida",
                  value: "FL",
                  sortOrder: 11,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Georgia",
                  value: "GA",
                  sortOrder: 12,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Hawaii",
                  value: "HI",
                  sortOrder: 13,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Idaho",
                  value: "ID",
                  sortOrder: 14,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Illinois",
                  value: "IL",
                  sortOrder: 15,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Indiana",
                  value: "IN",
                  sortOrder: 16,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Iowa",
                  value: "IA",
                  sortOrder: 17,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Kansas",
                  value: "KS",
                  sortOrder: 18,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Kentucky",
                  value: "KY",
                  sortOrder: 19,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Louisiana",
                  value: "LA",
                  sortOrder: 20,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Maine",
                  value: "ME",
                  sortOrder: 21,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Maryland",
                  value: "MD",
                  sortOrder: 22,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Massachusetts",
                  value: "MA",
                  sortOrder: 23,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Michigan",
                  value: "MI",
                  sortOrder: 24,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Minnesota",
                  value: "MN",
                  sortOrder: 25,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Mississippi",
                  value: "MS",
                  sortOrder: 26,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Missouri",
                  value: "MO",
                  sortOrder: 27,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Montana",
                  value: "MT",
                  sortOrder: 28,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Nebraska",
                  value: "NE",
                  sortOrder: 29,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Nevada",
                  value: "NV",
                  sortOrder: 30,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "New Hampshire",
                  value: "NH",
                  sortOrder: 31,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "New Jersey",
                  value: "NJ",
                  sortOrder: 32,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "New Mexico",
                  value: "NM",
                  sortOrder: 33,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "New York",
                  value: "NY",
                  sortOrder: 34,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "North Carolina",
                  value: "NC",
                  sortOrder: 35,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "North Dakota",
                  value: "ND",
                  sortOrder: 36,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Ohio",
                  value: "OH",
                  sortOrder: 37,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Oklahoma",
                  value: "OK",
                  sortOrder: 38,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Oregon",
                  value: "OR",
                  sortOrder: 39,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Pennsylvania",
                  value: "PA",
                  sortOrder: 40,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Rhode Island",
                  value: "RI",
                  sortOrder: 41,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "South Carolina",
                  value: "SC",
                  sortOrder: 42,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "South Dakota",
                  value: "SD",
                  sortOrder: 43,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Tennessee",
                  value: "TN",
                  sortOrder: 44,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Texas",
                  value: "TX",
                  sortOrder: 45,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Utah",
                  value: "UT",
                  sortOrder: 46,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Vermont",
                  value: "VT",
                  sortOrder: 47,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Virginia",
                  value: "VA",
                  sortOrder: 48,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Washington",
                  value: "WA",
                  sortOrder: 49,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "West Virginia",
                  value: "WV",
                  sortOrder: 50,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Wisconsin",
                  value: "WI",
                  sortOrder: 51,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Wyoming",
                  value: "WY",
                  sortOrder: 52,
                  description: null,
                  group: null,
                  default: false,
                },
              ],
              columnOrCustomFieldName: "state",
              tableName: "address",
              argumentTemplate: null,
              arguments: [
                {
                  tableName: "participant_address_type",
                  columnName: "participant_address_type_id",
                  value: 5,
                },
              ],
            },
            {
              label: "Zip Code",
              description: null,
              fieldName: "zip",
              type: "text",
              defaultValue: "",
              sortOrder: 5,
              size: "sm",
              info: null,
              google: true,
              required: {
                value: true,
                message: "Zip Code is required",
              },
              minLength: null,
              minValue: null,
              maxLength: {
                value: 6,
                message: "Zip Code must be less than 6 characters",
              },
              maxValue: null,
              pattern: {
                value: "^[0-9]{5}(?:-[0-9]{4})?$",
                message: "Zip Code must be a valid US Zip Code",
              },
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "zip",
              tableName: "address",
              argumentTemplate: null,
              arguments: [
                {
                  tableName: "participant_address_type",
                  columnName: "participant_address_type_id",
                  value: 5,
                },
              ],
            },
          ],
        },
        {
          title: null,
          sortOrder: 3,
          conditionallyDisplay: [],
          elements: [
            {
              label: "mailing address same as primary address",
              description: null,
              fieldName: "mailingSameAsPrimary",
              type: "conditionalCheckbox",
              defaultValue: "true",
              sortOrder: 1,
              size: "xl",
              info: null,
              google: true,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "mailingSameAsPrimary",
              tableName: "participant",
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
        {
          title: "Mailing Residency",
          sortOrder: 4,
          conditionallyDisplay: [
            {
              fieldName: "mailingSameAsPrimary",
              values: ["true"],
            },
          ],
          elements: [
            {
              label: "Address",
              description: null,
              fieldName: "mailaddress",
              type: "text",
              defaultValue: "",
              sortOrder: 1,
              size: "xl",
              info: null,
              google: true,
              required: {
                value: true,
                message: "Address is required",
              },
              minLength: null,
              minValue: null,
              maxLength: {
                value: 255,
                message: "Address must be less than 255 characters",
              },
              maxValue: null,
              pattern: {
                value: "^[a-zA-Z0-9\\s\\-\\#\\.\\,\\']+$",
                message: "Address must be alphanumeric",
              },
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "street_address",
              tableName: "address",
              argumentTemplate: null,
              arguments: [
                {
                  tableName: "participant_address_type",
                  columnName: "participant_address_type_id",
                  value: 1,
                },
              ],
            },
            {
              label: "Address 2",
              description: null,
              fieldName: "mailaddress2",
              type: "text",
              defaultValue: "",
              sortOrder: 2,
              size: "md",
              info: null,
              google: true,
              required: {
                value: false,
                message: "",
              },
              minLength: null,
              minValue: null,
              maxLength: {
                value: 255,
                message: "Address 2 must be less than 256 characters",
              },
              maxValue: null,
              pattern: {
                value: "^[a-zA-Z0-9\\s\\-\\#\\.\\,\\']+$",
                message: "Address 2 must be alphanumeric",
              },
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "street_address_2",
              tableName: "address",
              argumentTemplate: null,
              arguments: [
                {
                  tableName: "participant_address_type",
                  columnName: "participant_address_type_id",
                  value: 1,
                },
              ],
            },
            {
              label: "City",
              description: null,
              fieldName: "mailcity",
              type: "text",
              defaultValue: "",
              sortOrder: 3,
              size: "lg",
              info: null,
              google: true,
              required: {
                value: true,
                message: "City is required",
              },
              minLength: null,
              minValue: null,
              maxLength: {
                value: 255,
                message: "City must be less than 255 characters",
              },
              maxValue: null,
              pattern: {
                value: "^[a-zA-Z\\s\\-\\']+$",
                message: "City must be alphabetic",
              },
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "city",
              tableName: "address",
              argumentTemplate: null,
              arguments: [
                {
                  tableName: "participant_address_type",
                  columnName: "participant_address_type_id",
                  value: 1,
                },
              ],
            },
            {
              label: "State",
              description: null,
              fieldName: "mailstate",
              type: "select",
              defaultValue: "",
              sortOrder: 4,
              size: "sm",
              info: null,
              google: true,
              required: {
                value: true,
                message: "State is required",
              },
              minLength: null,
              minValue: null,
              maxLength: {
                value: 2,
                message: "State must be 2 characters",
              },
              maxValue: null,
              pattern: {
                value: "^[a-zA-Z]{2}$",
                message: "State must be alphabetic",
              },
              conditionallyDisplay: [],
              triggers: [],
              options: [
                {
                  label: "Select a State",
                  value: "",
                  sortOrder: 1,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Alabama",
                  value: "AL",
                  sortOrder: 2,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Alaska",
                  value: "AK",
                  sortOrder: 3,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Arizona",
                  value: "AZ",
                  sortOrder: 4,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Arkansas",
                  value: "AR",
                  sortOrder: 5,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "California",
                  value: "CA",
                  sortOrder: 6,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Colorado",
                  value: "CO",
                  sortOrder: 7,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Connecticut",
                  value: "CT",
                  sortOrder: 8,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Delaware",
                  value: "DE",
                  sortOrder: 9,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "District Of Columbia",
                  value: "DC",
                  sortOrder: 10,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Florida",
                  value: "FL",
                  sortOrder: 11,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Georgia",
                  value: "GA",
                  sortOrder: 12,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Hawaii",
                  value: "HI",
                  sortOrder: 13,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Idaho",
                  value: "ID",
                  sortOrder: 14,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Illinois",
                  value: "IL",
                  sortOrder: 15,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Indiana",
                  value: "IN",
                  sortOrder: 16,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Iowa",
                  value: "IA",
                  sortOrder: 17,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Kansas",
                  value: "KS",
                  sortOrder: 18,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Kentucky",
                  value: "KY",
                  sortOrder: 19,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Louisiana",
                  value: "LA",
                  sortOrder: 20,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Maine",
                  value: "ME",
                  sortOrder: 21,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Maryland",
                  value: "MD",
                  sortOrder: 22,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Massachusetts",
                  value: "MA",
                  sortOrder: 23,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Michigan",
                  value: "MI",
                  sortOrder: 24,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Minnesota",
                  value: "MN",
                  sortOrder: 25,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Mississippi",
                  value: "MS",
                  sortOrder: 26,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Missouri",
                  value: "MO",
                  sortOrder: 27,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Montana",
                  value: "MT",
                  sortOrder: 28,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Nebraska",
                  value: "NE",
                  sortOrder: 29,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Nevada",
                  value: "NV",
                  sortOrder: 30,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "New Hampshire",
                  value: "NH",
                  sortOrder: 31,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "New Jersey",
                  value: "NJ",
                  sortOrder: 32,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "New Mexico",
                  value: "NM",
                  sortOrder: 33,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "New York",
                  value: "NY",
                  sortOrder: 34,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "North Carolina",
                  value: "NC",
                  sortOrder: 35,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "North Dakota",
                  value: "ND",
                  sortOrder: 36,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Ohio",
                  value: "OH",
                  sortOrder: 37,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Oklahoma",
                  value: "OK",
                  sortOrder: 38,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Oregon",
                  value: "OR",
                  sortOrder: 39,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Pennsylvania",
                  value: "PA",
                  sortOrder: 40,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Rhode Island",
                  value: "RI",
                  sortOrder: 41,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "South Carolina",
                  value: "SC",
                  sortOrder: 42,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "South Dakota",
                  value: "SD",
                  sortOrder: 43,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Tennessee",
                  value: "TN",
                  sortOrder: 44,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Texas",
                  value: "TX",
                  sortOrder: 45,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Utah",
                  value: "UT",
                  sortOrder: 46,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Vermont",
                  value: "VT",
                  sortOrder: 47,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Virginia",
                  value: "VA",
                  sortOrder: 48,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Washington",
                  value: "WA",
                  sortOrder: 49,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "West Virginia",
                  value: "WV",
                  sortOrder: 50,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Wisconsin",
                  value: "WI",
                  sortOrder: 51,
                  description: null,
                  group: null,
                  default: false,
                },
                {
                  label: "Wyoming",
                  value: "WY",
                  sortOrder: 52,
                  description: null,
                  group: null,
                  default: false,
                },
              ],
              columnOrCustomFieldName: "state",
              tableName: "address",
              argumentTemplate: null,
              arguments: [
                {
                  tableName: "participant_address_type",
                  columnName: "participant_address_type_id",
                  value: 1,
                },
              ],
            },
            {
              label: "Zip Code",
              description: null,
              fieldName: "mailzip",
              type: "text",
              defaultValue: "",
              sortOrder: 5,
              size: "sm",
              info: null,
              google: true,
              required: {
                value: true,
                message: "Please enter a valid zip code.",
              },
              minLength: null,
              minValue: null,
              maxLength: {
                value: 6,
                message: "Zip Code must be less than 6 characters",
              },
              maxValue: null,
              pattern: {
                value: "^\\d{5}(?:[-\\s]\\d{4})?$",
                message: "Please enter a valid zip code.",
              },
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: "zip",
              tableName: "address",
              argumentTemplate: null,
              arguments: [
                {
                  tableName: "participant_address_type",
                  columnName: "participant_address_type_id",
                  value: 1,
                },
              ],
            },
          ],
        },
        {
          title: null,
          sortOrder: 5,
          conditionallyDisplay: [],
          elements: [
            {
              label: null,
              description: null,
              fieldName: "idFront",
              type: "fileDisplay",
              defaultValue: null,
              sortOrder: 1,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: null,
              description: null,
              fieldName: "idBack",
              type: "fileDisplay",
              defaultValue: null,
              sortOrder: 2,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
      ],
      onPageNext: [],
      onFormSubmit: [],
    },
    {
      title: "Confirm Information",
      optional: false,
      sortOrder: 4,
      conditionallyDisplay: [],
      sections: [
        {
          title: "Personal Information",
          sortOrder: 1,
          conditionallyDisplay: [],
          elements: [
            {
              label: "Title",
              description: null,
              fieldName: "title",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 1,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "First Name",
              description: null,
              fieldName: "firstName",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 2,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Middle Name",
              description: null,
              fieldName: "middleName",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 3,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Last Name",
              description: null,
              fieldName: "lastName",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 4,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Suffix",
              description: null,
              fieldName: "suffix",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 5,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Email",
              description: null,
              fieldName: "email",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 6,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Phone",
              description: null,
              fieldName: "phone",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 7,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Date Of Birth",
              description: null,
              fieldName: "dateOfBirth",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 8,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
        {
          title: "Primary Residency",
          sortOrder: 2,
          conditionallyDisplay: [],
          elements: [
            {
              label: "Address",
              description: null,
              fieldName: "address",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 1,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Address 2",
              description: null,
              fieldName: "address2",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 2,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "City",
              description: null,
              fieldName: "city",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 3,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "State",
              description: null,
              fieldName: "state",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 4,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Zip Code",
              description: null,
              fieldName: "zip",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 5,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
        {
          title: "Mailing Residency",
          sortOrder: 3,
          conditionallyDisplay: [
            {
              fieldName: "mailingSameAsPrimary",
              values: ["true"],
            },
          ],
          elements: [
            {
              label: "Address",
              description: null,
              fieldName: "mailaddress",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 1,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Address 2",
              description: null,
              fieldName: "mailaddress2",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 2,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "City",
              description: null,
              fieldName: "mailcity",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 3,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "State",
              description: null,
              fieldName: "mailstate",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 4,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
            {
              label: "Zip Code",
              description: null,
              fieldName: "mailzip",
              type: "fieldDisplay",
              defaultValue: null,
              sortOrder: 5,
              size: "full",
              info: null,
              google: false,
              required: null,
              minLength: null,
              minValue: null,
              maxLength: null,
              maxValue: null,
              pattern: null,
              conditionallyDisplay: [],
              triggers: [],
              options: [],
              columnOrCustomFieldName: null,
              tableName: null,
              argumentTemplate: null,
              arguments: [],
            },
          ],
        },
      ],
      onPageNext: [],
      onFormSubmit: [
        {
          api: "/license/participant",
          method: "POST",
          requestSlug: [],
          queryString: [],
          body: {
            type: "form-data",
            sendAllFormDataField: false,
            fields: [
              {
                key: "address",
                valueLocation: "form-data",
                valueName: "address",
              },
              {
                key: "address2",
                valueLocation: "form-data",
                valueName: "address2",
              },
              {
                key: "city",
                valueLocation: "form-data",
                valueName: "city",
              },
              {
                key: "dateOfBirth",
                valueLocation: "form-data",
                valueName: "dateOfBirth",
              },
              {
                key: "email",
                valueLocation: "form-data",
                valueName: "email",
              },
              {
                key: "firstName",
                valueLocation: "form-data",
                valueName: "firstName",
              },
              {
                key: "lastName",
                valueLocation: "form-data",
                valueName: "lastName",
              },
              {
                key: "mailaddress",
                valueLocation: "form-data",
                valueName: "mailaddress",
              },
              {
                key: "mailaddress2",
                valueLocation: "form-data",
                valueName: "mailaddress2",
              },
              {
                key: "mailcity",
                valueLocation: "form-data",
                valueName: "mailcity",
              },
              {
                key: "mailstate",
                valueLocation: "form-data",
                valueName: "mailstate",
              },
              {
                key: "mailzip",
                valueLocation: "form-data",
                valueName: "mailzip",
              },
              {
                key: "middleName",
                valueLocation: "form-data",
                valueName: "middleName",
              },
              {
                key: "phone",
                valueLocation: "form-data",
                valueName: "phone",
              },
              {
                key: "state",
                valueLocation: "form-data",
                valueName: "state",
              },
              {
                key: "suffix",
                valueLocation: "form-data",
                valueName: "suffix",
              },
              {
                key: "title",
                valueLocation: "form-data",
                valueName: "title",
              },
              {
                key: "zip",
                valueLocation: "form-data",
                valueName: "zip",
              },
            ],
          },
          response: {
            success: {
              code: 201,
              waitUntilComplete: true,
              setAllToForm: false,
              navigate: "profile",
              fields: [],
            },
            error: {
              code: [400, 500],
              message: "Some error message here for toast",
            },
          },
        },
      ],
    },
  ],
  requiredFields: [],
};
