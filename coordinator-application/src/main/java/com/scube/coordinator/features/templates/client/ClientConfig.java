package com.scube.coordinator.features.templates.client;

import com.scube.client.config.ProxyBeanUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ClientConfig {
    @Value("${com.scube.client.documentTemplate}")
    String documentTemplateURL;

    @Bean
    public IDocumentTemplateHttpExchange documentTemplateHttpExchange() {
        var webClient = ProxyBeanUtil.createWebClient(documentTemplateURL);
        return ProxyBeanUtil.createHttpProxyClient(webClient, IDocumentTemplateHttpExchange.class);
    }
}
