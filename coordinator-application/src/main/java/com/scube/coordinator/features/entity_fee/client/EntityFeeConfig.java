package com.scube.coordinator.features.entity_fee.client;

import com.scube.client.config.ProxyBeanUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class EntityFeeConfig {
    private final WebClient webClient;

    public EntityFeeConfig(@Value("${com.scube.client.license}") String serviceUrl) {
        this.webClient = ProxyBeanUtil.createWebClient(serviceUrl);
    }

    @Bean
    public IEntityFeeExchange testExchange() {
        return ProxyBeanUtil.createHttpProxyClient(this.webClient, IEntityFeeExchange.class);
    }
}
