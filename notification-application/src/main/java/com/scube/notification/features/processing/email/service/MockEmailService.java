package com.scube.notification.features.processing.email.service;

import com.scube.notification.features.processing.email.model.Email;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.scube.notification.features.processing.email.constants.ServiceNames.MOCK_EMAIL;

@Service(MOCK_EMAIL)
@Slf4j
@AllArgsConstructor
public class MockEmailService implements IEmailService {
    @Override
    public void send(Email email) {
        log.info("MockEmailService.send()");
        log.info("SUCCESS: {}", email);
        log.debug("Content: {}", email.getBody());
    }
}
