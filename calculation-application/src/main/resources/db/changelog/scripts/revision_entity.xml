<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="alterColumn_audit_log_revision_reset_the_pk4" author="David">
        <sql splitStatements="false">
            <![CDATA[
                DO $$
                DECLARE
                    max_revision_id BIGINT;
                BEGIN
                    -- Get the current max value of audit_log_revision_id and add 1
                    SELECT COALESCE(MAX(audit_log_revision_id),0) + 1
                    INTO max_revision_id
                    FROM calculation.audit_log_revision;

                    -- Drop the identity from the column (if it exists)
                    BEGIN
                        EXECUTE 'ALTER TABLE calculation.audit_log_revision ALTER COLUMN audit_log_revision_id DROP IDENTITY';
                    EXCEPTION
                        WHEN others THEN
                            -- Identity might not exist, so ignore any error
                            NULL;
                    END;

                    -- Drop the default value on the column
                    EXECUTE 'ALTER TABLE calculation.audit_log_revision ALTER COLUMN audit_log_revision_id DROP DEFAULT';

                    -- Alter the column to use GENERATED ALWAYS AS IDENTITY
                    EXECUTE format('ALTER TABLE calculation.audit_log_revision
                    ALTER COLUMN audit_log_revision_id SET DATA TYPE BIGINT,
                    ALTER COLUMN audit_log_revision_id ADD GENERATED ALWAYS AS IDENTITY
                    (START WITH %s INCREMENT 1)', max_revision_id
                    );
                END $$;
            ]]>
        </sql>
    </changeSet>
</databaseChangeLog>