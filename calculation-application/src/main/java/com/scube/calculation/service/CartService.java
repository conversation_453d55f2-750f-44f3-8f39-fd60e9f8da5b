package com.scube.calculation.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.audit.auditable.entity.AuditableBase;
import com.scube.calculation.dto.*;
import com.scube.calculation.dto.PayableSummaryResponse.PayableSummaryItem;
import com.scube.calculation.enums.CartStatus;
import com.scube.calculation.mapper.CartItemMapper;
import com.scube.calculation.mapper.CartMapper;
import com.scube.calculation.model.*;
import com.scube.calculation.repository.*;
import com.scube.config_utils.json_storage.utils.JsonStorageUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Slf4j
@Service
@RequiredArgsConstructor
public class CartService {

    public static final int DEFAULT_PAYABLE = 0; // TODO The root of an item should probably have a payable?
    public static final List<String> NON_UNIQUE_ITEMS_TYPE_ID = List.of("license");

    private final CartRepository cartRepository;
    private final FeeRepository feeRepository;
    private final CartItemRepository cartItemRepository;
    private final CartItemFeeRepository cartItemFeeRepository;
    private final CartMapper cartMapper;
    private final CartItemMapper cartItemMapper;
    private final CartAdditionalFee cartAdditionalFee;
    private final CartAdditionalItemFee cartAdditionalItemFee;

    @Transactional
    public void suspendAllUsersCart(String userId) {
        cartRepository.updateStatusForCarts(userId, List.of(CartStatus.CART_ACTIVE), CartStatus.CART_SUSPENDED);
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public CartInvoiceResponse createCart(String userId) {
        suspendAllUsersCart(userId);

        Cart cart = new Cart(userId);
        cartRepository.save(cart);

        return getCartInvoice(cart.getId());
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    public CartInvoiceResponse getActiveCartInvoice(String userId, String paymentMethod) {
        Cart cart = cartRepository.getActiveCartByUserId(userId);
        if (cart == null) return createCart(userId);
        clearSurchargesAndGetCart(cart);
        cart = addSurchargeItem(cart, paymentMethod);

        var dto = cartMapper.toDto(cart);
        return new CartInvoiceResponse(dto);
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public void switchCart(UUID cartId, String userId) {
        Cart cart = getCartById(cartId);
        if (cart.getStatus() != CartStatus.CART_SUSPENDED)
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Only suspended carts can be activated");
        suspendAllUsersCart(userId);
        cart.setUserId(userId);
        cart.setStatus(CartStatus.CART_ACTIVE);
        cartRepository.save(cart);
    }

    public PageDTO<CartSummaryResponse> listCarts(String userId, List<String> statusesParam, int pageNumber, int pageSize) {
        pageNumber -= 1; // The pagination should be 1-indexed, but Spring is 0-indexed by default
        List<CartStatus> statuses = new ArrayList<>();
        if (ObjectUtils.isEmpty(statusesParam)) {
            statuses = List.of(CartStatus.CART_ACTIVE, CartStatus.CART_SUSPENDED);//all carts but exclude orders
        } else {
            for (var statusString : statusesParam) {
                var status = CartStatus.valueOfIgnoreCase(statusString);
                if (status == null)
                    throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid cart status: " + statusString);
                statuses.add(status);
            }
        }
        Page<Cart> cartPage = cartRepository.findFilteredPaginatedCarts(userId, statuses, PageRequest.of(pageNumber, pageSize, Sort.by(AuditableBase.LAST_MODIFIED_DATE_CAMEL_CASE).descending()));
        List<CartSummaryResponse> summaries = cartPage.stream().map(x -> new CartSummaryResponse(cartMapper.toDto(x))).toList();
        PageDTO<CartSummaryResponse> response = new PageDTO<>();
        response.setItems(summaries);
        response.setPageIndex(cartPage.getNumber() + 1);
        response.setPageSize(cartPage.getSize());
        response.setTotalCount(cartPage.getTotalElements());
        return response;
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void clearSurchargesAndGetCart(Cart cart) {
        if (cart != null && !cart.getAdditionalFees().isEmpty()) {
            var additionalFees = cart.getAdditionalFees();


            var additionalItemFees = additionalFees.stream()
                    .flatMap(fee -> fee.getAdditionalItemFees().stream())
                    .toList();


            if (!additionalItemFees.isEmpty()) {
                var itemFeeIds = additionalItemFees.stream()
                        .map(AdditionalItemFee::getId)
                        .collect(Collectors.toList());

                cartAdditionalItemFee.deleteAllByIdInBatch(itemFeeIds);
            }

            var additionalFeeIds = additionalFees.stream()
                    .map(AdditionalFee::getId)
                    .collect(Collectors.toList());

            cartAdditionalFee.deleteAllByIdInBatch(additionalFeeIds);

            cart.setAdditionalFees(new ArrayList<>());
        }
    }

    private List<AdditionalFee> requestToAdditionalFee(List<AdditionalFeeRequest> request, Cart cart) {
        List<AdditionalFee> additionalFees = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        if (request == null)
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Fee list cannot be null");

        for (AdditionalFeeRequest additionalFeeRequest : request) {
            List<Fee> fees = new ArrayList<>();
            for(String feeCode: additionalFeeRequest.getFeeCode()){
                var fee = feeRepository.findByKey(feeCode)
                        .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "No such fee: " + additionalFeeRequest.getFeeCode()));
                if(!ObjectUtils.isEmpty(fee)){
                    fees.add(fee);
                }
            }


            var item_c = new AdditionalFee(cart, fees, additionalFeeRequest.getLabel(),"ADDITIONAL_FEE");
            item_c.setProperties(mapper.convertValue(additionalFeeRequest.getFeeJson(), new TypeReference<>() {}));
            additionalFees.add(item_c);
        }

        return additionalFees;
    }

    private CartItem requestToItem(AddItemRequest request) {
        CartItem item = new CartItem(request);
        if (request.getFees() == null)
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Fee list cannot be null");

        var itemFees = new ArrayList<CartItemFee>();
        for (FeeRequest feeRequest : request.getFees()) {
            var fee = feeRepository.findByKey(feeRequest.getFeeCode())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "No such fee: " + feeRequest.getFeeCode()));
            itemFees.add(new CartItemFee(item, fee, feeRequest));
        }

        item.setCartItemFees(itemFees);
        return item;
    }

    @Transactional(propagation = Propagation.MANDATORY)
    public Cart addSurchargeItem(Cart cart, String paymentMethod) {
        if (cart == null || cart.getStatus() != CartStatus.CART_ACTIVE) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Items can only be added to an active cart");
        }

        List<AdditionalFeeRequest> surchargeRequests = getSurchargeRequestsForPaymentMethod(paymentMethod);
        cart.addAdditionalFee(requestToAdditionalFee(surchargeRequests, cart));
        return cartRepository.save(cart);
    }

    @Transactional(propagation = Propagation.MANDATORY)
    public List<AdditionalFeeRequest> getSurchargeRequestsForPaymentMethod(String paymentMethod) {
        if (paymentMethod == null) {
            return List.of();
        }

        return JsonStorageUtils.get(Map.of("payment", "paymentForm"))
                .map(paymentForm -> StreamSupport.stream(paymentForm.spliterator(), false)
                .filter(item -> paymentMethod.equals(item.get("key").asText()))
                .findFirst()
                .map(matchedPayment -> matchedPayment.get("surcharge"))
                .filter(surchargeNode -> surchargeNode.isArray() && !surchargeNode.isEmpty())
                .map(surchargeNode -> StreamSupport.stream(surchargeNode.spliterator(), false)
                        .map(item -> new AdditionalFeeRequest(
                                StreamSupport.stream(item.get("feeCodes").spliterator(), false)
                                        .map(JsonNode::asText)
                                        .collect(Collectors.toList()),
                                item.get("label").asText(),
                                item.has("minFee") ? BigDecimal.valueOf(item.get("minFee").asDouble()) : BigDecimal.ZERO,
                                item
                        ))
                        .collect(Collectors.toList()))
                .orElse(List.of()))
        .orElseThrow(() -> new RuntimeException("Payment form not found"));
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    public CartItemSummary addItem(UUID id, AddItemRequest request) {
        Cart cart = getCartById(id);
        if (cart.getStatus() != CartStatus.CART_ACTIVE)
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Items can only be added to an active cart");

        cart.addItem(requestToItem(request));

        cartRepository.save(cart);

        var lastItem = cart.getCartItems().getLast();
        return new CartItemSummary(cartItemMapper.toDto(lastItem));
    }

    @Transactional
    public void removeItemByCartItemIds(UUID cartId, List<Long> cartItemIds) {
        for (Long cartItemId : cartItemIds) {
            removeItem(cartId, cartItemId);
        }
    }

    @Transactional
    public void removeItemByItemIds(UUID cartId, List<UUID> itemIds) {
        for (UUID itemId : itemIds) {
            removeItem(cartId, itemId);
        }
    }

    @Transactional
    public void removeItemByItemIdsSilently(UUID cartId, List<UUID> itemIds) {
        for (UUID itemId : itemIds) {
            removeItemSilently(cartId, itemId);
        }
    }

    @Transactional
    public void removeItem(UUID cartId, long cartItemId) {
        CartItem item = cartItemRepository.findById(cartItemId)
                .orElse(null);

        if (item == null) return;

        Cart cart = item.getCart();
        if (!cart.getId().equals(cartId))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No such cart item");
        cartItemRepository.delete(item);
    }

    @Transactional
    public void removeItem(UUID cartId, UUID itemId) {
        List<CartItem> items = cartItemRepository.findByCartIdAndUniqueItemId(cartId, itemId);
        if (ObjectUtils.isEmpty(items))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "No such cart item with id " + itemId + " in cart " + cartId);
        for (CartItem item : items) {
            removeItem(cartId, item.getId());
        }
    }

    @Transactional
    public void removeItemSilently(UUID cartId, UUID itemId) {
        List<CartItem> items = cartItemRepository.findByCartIdAndUniqueItemId(cartId, itemId);
        if (ObjectUtils.isEmpty(items)) {
            return; // Silently ignore if items don't exist
        }
        for (CartItem item : items) {
            removeItem(cartId, item.getId());
        }
    }

    public CartInvoiceResponse getCartInvoice(UUID id) {
        Cart cart = getCartById(id);
        if (!Set.of(CartStatus.CART_ACTIVE, CartStatus.CART_SUSPENDED).contains(cart.getStatus())) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "The specified cart is no longer open");
        }
        return new CartInvoiceResponse(cartMapper.toDto(cart));
    }

    public PayableSummaryResponse getCartSummary(UUID id) {
        Cart cart = getCartById(id);
        if (!Set.of(CartStatus.CART_ACTIVE, CartStatus.CART_SUSPENDED).contains(cart.getStatus())) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "The specified cart is no longer open");
        }
        PayableSummaryResponse response = new PayableSummaryResponse();
        response.setItems(cart.getCartItems()
                .stream()
                .flatMap(item -> {
                            ArrayList<PayableSummaryItem> summaryItems = new ArrayList<>();
                            BigDecimal basePrice = item.getPrice();
                            for (var cartItemFee : item.getCartItemFees()) {
                                var fee = cartItemFee.getFee();
                                BigDecimal feePrice = fee.getFeePrice(basePrice, cartItemFee.calculatePrice());

                                summaryItems.add(new PayableSummaryItem(fee.getPayableId(), feePrice));
                            }
                            summaryItems.add(new PayableSummaryItem(DEFAULT_PAYABLE, basePrice));
                            return summaryItems.stream();
                        }
                ).toList());
        response.setTotal(response.getItems().stream().map(PayableSummaryItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        return response;
    }

    public CartInvoiceResponse getPreviewInvoice(List<AddItemRequest> itemRequests) {
        List<CartItem> items = itemRequests.stream().map(this::requestToItem).toList();
        var cart = new Cart();
        cart.setCartItems(items);
        return new CartInvoiceResponse(cartMapper.toDto(cart));
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void clearCart(UUID id) {
        Cart cart = getCartById(id);
        if (!Set.of(CartStatus.CART_ACTIVE, CartStatus.CART_SUSPENDED).contains(cart.getStatus())) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "There is no open cart with that id");
        }

        clearSurchargesAndGetCart(cart);
        cartItemRepository.deleteAllInBatch(cart.getCartItems());
    }

    @Transactional
    public void deleteCart(UUID id) {
        Cart cart = getCartById(id);
        if (!Set.of(CartStatus.CART_ACTIVE, CartStatus.CART_SUSPENDED).contains(cart.getStatus())) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "There is no open cart with that id");
        }
        if (!cart.getCartItems().isEmpty()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "The specified cart cannot be deleted, as it contains items");
        }
        cartRepository.delete(cart);
    }

    private Cart getCartById(UUID id) {
        return cartRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Cart with id " + id + " not found"));
    }

    public List<CartInvoiceResponse> findCartsByFilters(Map<String, String> queryParams) {
        if (queryParams.isEmpty())
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "At least one filter is required");

        return cartRepository.findCartsByFilters(queryParams).stream()
                .map(x -> new CartInvoiceResponse(cartMapper.toDto(x))).toList();
    }

    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public void removeOrderItemsFromActiveUserCart(Order order) {
        Cart cart = cartRepository.getActiveCartByUserId(order.getUserId());

        HashSet<CartItem> itemsToRemove = new HashSet<>();
        for (OrderItem orderItem : order.getOrderItems()) {
            itemsToRemove.addAll(cart.getCartItems().stream()
                    .filter(cartItem -> cartItem.getUniqueItemId().equals(orderItem.getUniqueItemId()))
                    .toList());
        }
        clearSurchargesAndGetCart(cart);
        cartItemRepository.deleteAllInBatch(itemsToRemove);
    }

    public CartInvoiceResponse.CartInvoiceItem getCartItem(long cartItemId) {
        CartItem item = cartItemRepository.findById(cartItemId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Cart item with id %d not found".formatted(cartItemId)));
        return new CartInvoiceResponse.CartInvoiceItem(cartItemMapper.toDto(item));
    }

    public void changePriceOnCartItemFee(UUID cartItemId, UpdateCartItemFeeRequest request) {
        CartItemFee fee = cartItemFeeRepository.findByUuidOrThrow(cartItemId);
        fee.changePrice(request.getFeeAmount(), request.getReason());
        cartItemFeeRepository.save(fee);
    }

    @Transactional
    public void removeCartItemFee(UUID cartItemId) {
        cartItemFeeRepository.deleteByUuid(cartItemId);
    }

    @Transactional
    public void removeCartItemFeeByEntityFeeId(UUID entityFeeId) {
        cartItemFeeRepository.deleteByEntityFeeIdFromProperties(entityFeeId.toString());
    }


    @Transactional
    public void removeCartItemByItemId(UUID itemId) {
        cartItemRepository.deleteByUniqueItemId(itemId);
    }

    public void addFeeOnCartItem(Long cartItemId, AddFeeOnCartItemRequest request) {
        var cartItem = cartItemRepository.findById(cartItemId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "No such cart item: " + cartItemId));
        var fee = feeRepository.findByKey(request.getFeeCode())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "No such fee: " + request.getFeeCode()));
        var requestFee = new FeeRequest(request.getFeeCode(), request.getFeeAmount(), Map.of(
                "reason", request.getReason()
        ));
        var cartItemFee = new CartItemFee(cartItem, fee, requestFee);
        cartItemFeeRepository.save(cartItemFee);
    }

    @Transactional
    public void updateFeeOnCartItemByItemId(UUID itemId, AddFeeOnCartItemRequest request) {
        var itemFee = cartItemFeeRepository.findByEntityFeeIdFromProperties(itemId.toString());
        removeCartItemFeeByEntityFeeId(itemId);
        var cartItem = cartItemRepository.findById(itemFee.getItem().getId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "No such cart item: " + itemId));
        var fee = feeRepository.findByKey(request.getFeeCode())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "No such fee: " + request.getFeeCode()));
        var requestFee = new FeeRequest(request.getFeeCode(), request.getFeeAmount(), Map.of(
                "entityFeeId", itemId.toString()
        ));
        var cartItemFee = new CartItemFee(cartItem, fee, requestFee);
        cartItemFeeRepository.save(cartItemFee);
    }

    public void removeEntityFromAllCarts(List<UUID> entityIds){
        var cartItems = cartItemRepository.findByUniqueItemIdInAndItemTypeIdIn(entityIds, NON_UNIQUE_ITEMS_TYPE_ID);
        Map<UUID, List<UUID>> cartIdToItemIds = cartItems.stream()
                .collect(Collectors.groupingBy(
                        ci -> ci.getCart().getId(),
                        Collectors.mapping(CartItem::getUniqueItemId, Collectors.toList())
                ));
        cartIdToItemIds.forEach((x, cartIds) -> this.removeItemByItemIds(x, cartIdToItemIds.get(x)));
    }
}