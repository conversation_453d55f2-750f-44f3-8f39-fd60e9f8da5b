package com.scube.report.features.licensing.dog.dto.reports;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItem;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItemFee;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.report.features.base.serializers.BigDecimalTwoDecimalPlacesSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

import static com.scube.report.features.licensing.dog.util.DogLicenseReportUtil.*;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AnimalPopulationControlFundReport {
    @JsonProperty("MonthSubmitted")
    private String monthSubmitted;

    @JsonProperty("YearSubmitted")
    private String yearSubmitted;

    @JsonProperty("CityClerk")
    private String cityClerk;

    @JsonProperty("ClerkTitle")
    private String clerkTitle;

    @JsonProperty("PreparedDate")
    private String preparedDate;

    @JsonProperty("AlteredTotal")
    private Integer alteredTotal = 0;

    @JsonProperty("UnalteredTotal")
    private Integer unalteredTotal = 0;

    @JsonSerialize(using = BigDecimalTwoDecimalPlacesSerializer.class)
    @JsonProperty("AlteredFee")
    private BigDecimal alteredFee;

    @JsonSerialize(using = BigDecimalTwoDecimalPlacesSerializer.class)
    @JsonProperty("UnalteredFee")
    private BigDecimal unalteredFee;

    @JsonSerialize(using = BigDecimalTwoDecimalPlacesSerializer.class)
    @JsonProperty("AlteredFeeTotal")
    public BigDecimal getAlteredFeeTotal() {
        return alteredFee.multiply(BigDecimal.valueOf(alteredTotal));
    }

    @JsonSerialize(using = BigDecimalTwoDecimalPlacesSerializer.class)
    @JsonProperty("UnalteredFeeTotal")
    public BigDecimal getUnalteredFeeTotal() {
        return unalteredFee.multiply(BigDecimal.valueOf(unalteredTotal));
    }

    @JsonSerialize(using = BigDecimalTwoDecimalPlacesSerializer.class)
    @JsonProperty("TotalRemitted")
    private BigDecimal getTotalRemitted() {
        return getAlteredFeeTotal().add(getUnalteredFeeTotal());
    }

    @JsonProperty("CheckNumber")
    private String checkNumber;

    public void incrementAlteredTotal() {
        alteredTotal++;
    }

    public void incrementUnalteredTotal() {
        unalteredTotal++;
    }

    public void countStateFees(List<OrderInvoiceResponse> orders) {
        if (orders == null) return;

        for (OrderInvoiceResponse order : orders) {
            countStateFees(order);
        }
    }

    public void countStateFees(OrderInvoiceResponse order) {
        if (order == null) return;

        for (OrderInvoiceItem item : order.getItems()) {
            countStateFees(item);
        }
    }

    private void countStateFees(OrderInvoiceItem item) {
        if (item == null) return;

        if (isDogLicense(item)) {
            if (isStateExempt(item)) return;

            for (OrderInvoiceItemFee fee : item.getFees()) {
                if (isAlteredFee(fee)) {
                    incrementAlteredTotal();
                } else if (isUnalteredFee(fee)) {
                    incrementUnalteredTotal();
                }
            }
        }
    }
    //Set default values for builder if fields are not provided
    public static class AnimalPopulationControlFundReportBuilder {
        private Integer alteredTotal = 0;
        private Integer unalteredTotal = 0;
    }
}
