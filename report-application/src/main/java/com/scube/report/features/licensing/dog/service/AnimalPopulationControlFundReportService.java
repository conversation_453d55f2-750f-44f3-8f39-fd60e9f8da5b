package com.scube.report.features.licensing.dog.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.config_utils.json_storage.utils.JsonStorageUtils;
import com.scube.report.features.base.service.IReportQueryService;
import com.scube.report.features.licensing.dog.dto.reports.AnimalPopulationControlFundReport;
import com.scube.report.features.licensing.dog.dto.request.AnimalPopulationControlFundRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import static com.scube.report.features.base.util.ValidationUtil.validate;

@Service("AnimalPopulationControlFundForm")
@Slf4j
@RequiredArgsConstructor
public class AnimalPopulationControlFundReportService implements IReportQueryService {
    private final CalculationServiceConnection calculationService;
    private final CalculationServiceWrapper calculationServiceWrapper;
    private static final String REPORT_NAME = "AnimalPopulationControlFundForm";

    @Override
    public Object execute(Map<String, Object> params) {
        log.debug("ReportService.getAnimalPopulationControlFundData()");
        JsonNode tenantData = JsonStorageUtils.get(Map.of("config","tenant")).orElseThrow(() -> new RuntimeException("Tenant data not found"));

        AnimalPopulationControlFundRequest request = new AnimalPopulationControlFundRequest(params);
        validate(request);

        var fees = calculationService.fee().getAllFees();

        //State of NY Fees
        BigDecimal alteredFee = fees.stream()
                .filter(fee -> fee.getKey().equalsIgnoreCase("DL-S-ALT"))
                .findFirst()
                .map(fee -> fee.getAmount()).orElseThrow(() -> new RuntimeException("state altered Fee not found"));

        BigDecimal unalteredFee = fees.stream()
                .filter(fee -> fee.getKey().equalsIgnoreCase("DL-S-UNALT"))
                .findFirst()
                .map(fee -> fee.getAmount()).orElseThrow(() -> new RuntimeException("state unaltered Fee not found"));

        //Retrieve all the paid orders that have a dog license item from calc service
        List<OrderInvoiceResponse> licenseOrders = calculationServiceWrapper.getRegularDogOrders(params);
        List<OrderInvoiceResponse> purebredOrders = calculationServiceWrapper.getPurebredDogOrders(params);

        licenseOrders.addAll(purebredOrders);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        AnimalPopulationControlFundReport animalPopulationControlFundReport = AnimalPopulationControlFundReport.builder()
                .monthSubmitted(LocalDate.parse((String) params.get("startDate"), formatter).getMonth().toString())
                .yearSubmitted(String.valueOf(LocalDate.parse((String) params.get("startDate"), formatter).getYear()))
                .cityClerk(tenantData.get("clerkName").asText())
                .clerkTitle(tenantData.get("publisher").asText())
                .preparedDate(LocalDate.now().format(DateTimeFormatter.ofPattern("MM/dd/yyyy")))
                .alteredFee(alteredFee)
                .unalteredFee(unalteredFee)
                .checkNumber((String) params.get("checkNumber"))
                .build();

        animalPopulationControlFundReport.countStateFees(licenseOrders);

        return animalPopulationControlFundReport;
    }

    public String getReportName() {
        return REPORT_NAME;
    }
}
