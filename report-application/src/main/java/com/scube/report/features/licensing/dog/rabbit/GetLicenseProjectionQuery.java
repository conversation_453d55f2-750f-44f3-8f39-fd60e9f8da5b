package com.scube.report.features.licensing.dog.rabbit;


import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisherRpc;
import com.scube.report.features.licensing.dog.dto.LicenseProjection;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class GetLicenseProjectionQuery implements IRabbitFanoutPublisherRpc<LicenseProjection> {
    private String entityId;
}